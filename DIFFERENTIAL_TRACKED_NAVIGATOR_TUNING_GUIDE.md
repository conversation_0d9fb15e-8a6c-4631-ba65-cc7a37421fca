# Differential Tracked Navigator 算法调参指南

## 概述

本指南详细说明如何通过调整参数来优化差动履带车辆的Pure Pursuit跟踪控制性能。该导航系统采用分层距离控制策略，能够根据到目标点的距离动态调整控制响应性，实现精确而稳定的路径跟踪。

## 算法原理

### Pure Pursuit算法特点
- **分层距离控制**: 根据到目标点的距离分为远、中、近、很近四个控制层级
- **自适应角速度增益**: 不同距离范围使用不同的角速度控制增益
- **动态线速度调节**: 基于距离和配置因子动态计算线速度
- **障碍物安全集成**: 集成激光雷达障碍物检测和多级安全响应

### 控制流程
1. 计算到目标点的距离和角度误差
2. 根据距离选择相应的控制策略（远/中/近/很近）
3. 应用相应的角速度增益和距离调节因子
4. 计算并限制最终的线速度和角速度输出

## 参数分类详解

### 1. 车辆物理参数 (`vehicle.*`)

#### `vehicle.track_width` (默认: 1.69m)
- **作用**: 车辆履带间距，用于障碍物检测的几何约束
- **调参影响**: 
  - 增大: 障碍物检测范围变宽，安全性提高但可能过于保守
  - 减小: 检测范围变窄，通过性提高但安全余量减少
- **调参建议**: 根据实际车辆尺寸设置，建议加0.2-0.5m安全余量

#### `vehicle.max_linear_velocity` (默认: 1.5 m/s)
- **作用**: 限制最大前进速度
- **调参影响**:
  - 增大: 提高运动效率，但降低控制精度和安全性
  - 减小: 提高控制精度和安全性，但降低运动效率
- **调参建议**: 根据车辆性能和应用场景平衡，建议从1.0m/s开始调试

#### `vehicle.min_linear_velocity` (默认: 0.0 m/s)
- **作用**: 最小前进速度约束，防止过慢运动
- **调参影响**:
  - 增大: 减少停滞现象，但可能导致目标点附近振荡
  - 减小: 允许更精细的速度控制，但可能出现停滞
- **调参建议**: 通常设为0.1-0.3m/s，根据车辆最小稳定运行速度确定

#### `vehicle.max_angular_velocity` (默认: 1.6 rad/s)
- **作用**: 限制最大转向角速度
- **调参影响**:
  - 增大: 提高转向响应速度，但可能导致不稳定
  - 减小: 提高稳定性，但转向响应变慢
- **调参建议**: 根据车辆转向性能设置，建议从1.0 rad/s开始调试

### 2. Pure Pursuit控制参数 (`control.*`)

#### `control.goal_tolerance` (默认: 0.3m)
- **作用**: 到达目标点的距离容差
- **调参影响**:
  - 增大: 降低到达精度但提高通过效率
  - 减小: 提高到达精度但可能导致振荡或无法到达
- **调参建议**: 根据应用精度要求设置，一般为车身长度的0.2-0.5倍

#### `control.angle_deadzone_degrees` (默认: 1.5°)
- **作用**: 角度控制死区，小于此角度不进行转向
- **调参影响**:
  - 增大: 减少小角度振荡，但降低方向精度
  - 减小: 提高方向精度，但可能增加振荡
- **调参建议**: 通常设置1-3°，根据车辆转向精度调整

#### `control.linear_velocity_factor` (默认: 0.5)
- **作用**: 线速度计算系数 (速度 = 距离 × 系数)
- **调参影响**:
  - 增大: 提高运动速度，但可能降低控制精度
  - 减小: 提高控制精度，但降低运动效率
- **调参建议**: 0.3-0.8范围内调整，平衡速度和精度

### 3. 分层距离控制参数 (`control.*_distance_*`)

#### 距离阈值参数
- `control.far_distance_threshold` (默认: 3.0m): 远距离控制阈值
- `control.medium_distance_threshold` (默认: 1.5m): 中距离控制阈值  
- `control.close_distance_threshold` (默认: 0.8m): 近距离控制阈值

**调参策略**:
- 根据车辆尺寸和运动特性设置梯度合理的距离分层
- 远距离阈值: 建议为车长的2-4倍
- 中距离阈值: 建议为车长的1-2倍
- 近距离阈值: 建议为车长的0.5-1倍

#### 角速度增益参数
- `control.far_distance_angular_gain` (默认: 2.5): 远距离角速度增益
- `control.medium_distance_angular_gain` (默认: 2.0): 中距离角速度增益
- `control.close_distance_angular_gain` (默认: 1.5): 近距离角速度增益
- `control.very_close_angular_gain` (默认: 1.0): 很近距离角速度增益

**调参策略**:
- 遵循远距离高增益，近距离低增益的原则
- 远距离增益: 2.0-3.5，保证快速响应
- 中距离增益: 1.5-2.5，平衡响应性和稳定性
- 近距离增益: 1.0-2.0，优先稳定性
- 很近距离增益: 0.5-1.5，防止振荡

#### 距离调节因子
- `control.close_distance_factor` (默认: 0.8): 近距离距离调节因子
- `control.min_distance_factor` (默认: 0.5): 最小距离调节因子

**调参策略**:
- 用于在近距离时进一步降低角速度响应
- close_distance_factor: 0.6-0.9，近距离稳定性调节
- min_distance_factor: 0.3-0.7，防止过近时的振荡

### 4. 安全参数 (`safety.*`)

#### `safety.obstacle_detection_range` (默认: 4.5m)
- **作用**: 障碍物检测的最大范围
- **调参影响**:
  - 增大: 提高安全性，但可能过于保守
  - 减小: 提高通过性，但降低安全余量
- **调参建议**: 根据车辆速度和制动距离设置，建议为最大速度×3秒的距离

#### `safety.safety_stop_distance` (默认: 3.0m)
- **作用**: 安全停车距离，检测到此距离内障碍物时减速停车
- **调参影响**:
  - 增大: 提高安全性，但降低通过效率
  - 减小: 提高通过效率，但降低安全性
- **调参建议**: 建议为最大速度×2秒的制动距离

#### `safety.emergency_stop_distance` (默认: 0.5m)
- **作用**: 紧急停车距离，检测到此距离内障碍物时立即停车
- **调参影响**:
  - 增大: 提高紧急安全性
  - 减小: 提高通过能力，但存在碰撞风险
- **调参建议**: 建议设置为车辆紧急制动距离加安全余量

## 调参方法论

### 1. 基础调参流程

#### 第一步: 车辆基础参数设置
1. 根据实际车辆设置 `vehicle.track_width`
2. 保守设置速度限制参数 (`max_linear_velocity` = 1.0, `max_angular_velocity` = 1.0)
3. 禁用障碍物检测 (`safety.enable_obstacle_detection` = false) 进行基础调试

#### 第二步: 基础控制参数调优
1. 调整 `control.goal_tolerance` 确保能稳定到达目标点
2. 调整 `control.linear_velocity_factor` 获得合适的运动速度
3. 调整 `control.angle_deadzone_degrees` 消除小角度振荡

#### 第三步: 分层控制优化
1. 设置合理的距离阈值梯度
2. 从远到近逐步调整角速度增益
3. 使用距离调节因子优化近距离稳定性

#### 第四步: 安全系统调试
1. 启用障碍物检测
2. 调整检测范围和安全距离
3. 验证紧急停车功能

#### 第五步: 性能优化
1. 逐步提高速度限制
2. 优化各层级增益获得最佳性能
3. 进行综合场景测试

### 2. 常见问题及解决方案

#### 问题: 车辆转向过慢
**症状**: 大角度转向时响应迟钝，路径跟踪滞后
**解决方案**:
1. 增大 `far_distance_angular_gain` (2.5 → 3.0)
2. 增大 `medium_distance_angular_gain` (2.0 → 2.5)
3. 检查 `max_angular_velocity` 是否过小

#### 问题: 近距离振荡
**症状**: 接近目标点时出现左右摆动
**解决方案**:
1. 减小 `very_close_angular_gain` (1.0 → 0.7)
2. 减小 `min_distance_factor` (0.5 → 0.3)
3. 增大 `angle_deadzone_degrees` (1.5° → 2.5°)
4. 增大 `goal_tolerance` (0.3 → 0.5)

#### 问题: 无法到达目标点
**症状**: 在目标点附近停止但不能到达容差范围内
**解决方案**:
1. 增大 `goal_tolerance` (0.3 → 0.5)
2. 检查 `min_linear_velocity` 设置是否合理
3. 减小近距离角速度增益避免过度转向

#### 问题: 直线跟踪不稳定
**症状**: 直线路径上出现蛇形运动
**解决方案**:
1. 增大 `angle_deadzone_degrees` (1.5° → 3.0°)
2. 减小所有角速度增益10-20%
3. 检查里程计数据质量

#### 问题: 转弯半径过大
**症状**: 转弯时不够敏锐，路径偏离较大
**解决方案**:
1. 增大相应距离范围的角速度增益
2. 减小 `close_distance_factor` (0.8 → 0.6)
3. 检查 `max_angular_velocity` 限制

### 3. 高级调参技巧

#### 自适应调参策略
1. **速度自适应**: 根据不同速度需求调整 `linear_velocity_factor`
2. **场景自适应**: 不同应用场景使用不同参数配置文件
3. **动态调参**: 运行时根据性能指标动态调整参数

#### 参数关联性分析
1. **速度-稳定性平衡**: 速度参数与角速度增益的协调
2. **精度-效率平衡**: 容差参数与增益参数的平衡
3. **安全-性能平衡**: 安全参数与控制参数的协调

## 调参工具和方法

### 1. 参数运行时调整
```bash
# 查看当前参数
ros2 param get /differential_tracked_navigator control.linear_velocity_factor

# 动态调整参数
ros2 param set /differential_tracked_navigator control.far_distance_angular_gain 3.0

# 批量查看参数
ros2 param list /differential_tracked_navigator
```

### 2. 配置文件调整
修改 `src/differential_tracked/config/navigator_params.yaml` 文件中的相应参数值，重新启动节点使更改生效。

### 3. 性能监控
使用提供的调试工具监控控制性能：
```bash
# 查看控制输出
ros2 topic echo /cmd_vel

# 查看当前目标点
ros2 topic echo /way_point

# 查看里程计数据
ros2 topic echo /state_estimation
```

## 推荐调参起点

### 保守配置 (适合初始调试)
```yaml
vehicle.max_linear_velocity: 1.0
vehicle.max_angular_velocity: 1.0
control.linear_velocity_factor: 0.4
control.far_distance_angular_gain: 2.0
control.medium_distance_angular_gain: 1.5
control.close_distance_angular_gain: 1.0
control.very_close_angular_gain: 0.7
```

### 平衡配置 (适合一般应用)
```yaml
vehicle.max_linear_velocity: 1.5
vehicle.max_angular_velocity: 1.6
control.linear_velocity_factor: 0.5
control.far_distance_angular_gain: 2.5
control.medium_distance_angular_gain: 2.0
control.close_distance_angular_gain: 1.5
control.very_close_angular_gain: 1.0
```

### 激进配置 (适合高性能需求)
```yaml
vehicle.max_linear_velocity: 2.0
vehicle.max_angular_velocity: 2.0
control.linear_velocity_factor: 0.6
control.far_distance_angular_gain: 3.0
control.medium_distance_angular_gain: 2.5
control.close_distance_angular_gain: 2.0
control.very_close_angular_gain: 1.2
```

## 注意事项

1. **安全第一**: 始终在安全环境中进行参数调试
2. **渐进调整**: 每次只调整1-2个参数，观察效果后再继续
3. **记录备份**: 调参前备份当前有效配置
4. **综合测试**: 调参后进行多种场景的综合测试
5. **硬件考虑**: 参数设置需考虑车辆的实际物理限制

## 总结

通过系统性的参数调优，可以显著改善差动履带车辆的路径跟踪性能。关键是理解各参数的作用机制，采用科学的调参方法，在安全性、精度和效率之间找到最佳平衡点。建议从保守配置开始，逐步优化至满足应用需求的最佳状态。