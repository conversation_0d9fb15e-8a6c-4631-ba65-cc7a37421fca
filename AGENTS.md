# Repository Guidelines

## Project Structure & Module Organization
- `src/differential_tracked/`: ROS2 Python navigator; parameters in `config/`, maps in `maps/`, launchers in `launch/`.
- `src/global_traj_generate/`: C++ planner with custom `msg/` types, YAML tunables, and `data/point.txt` waypoints.
- `src/point_publisher/`: keyboard and API waypoint publisher; headers shared with downstream controllers.
- `src/vehicle_simulator/` & `src/velodyne_simulator/`: Gazebo worlds, URDF, lidar plugins for regression verification.
- `src/visualization_tools/` plus `Tools/`: RViz layouts, plotting, and diagnostics (`Tools/test_trajectory_calculation.py`).

## Build, Test, and Development Commands
- `source /opt/ros/humble/setup.bash`: required before building or launching.
- `colcon build --symlink-install`: build all packages; add `--packages-select` for targeted rebuilds.
- `source install/setup.bash`: refresh workspace overlay after every build.
- `ros2 launch differential_tracked diff_tracked_system.launch.py use_cpp_navigator:=true enable_rviz:=false`: default end-to-end stack; tweak arguments per scenario.
- `ros2 launch vehicle_simulator system_tunnel.launch`: start the tunnel world used by regression runs.

## Coding Style & Naming Conventions
- Default to 4-space indentation; keep headers before sources in `CMakeLists.txt` and prefer `snake_case` filenames.
- C++: `CamelCase` classes, `lowerCamelCase` methods, ROS2 logging macros, and parameters mirroring YAML keys (`vehicle.track_width` etc.).
- Python: follow ROS2 node patterns—explicit QoS setup, `declare_parameter` calls in `__init__`, and guarded `main()` entry points.
- YAML configs stay grouped by domain (`vehicle`, `control`, `safety`) and should only expose declared parameters.

## Testing Guidelines
- After builds, run `colcon test` and `python3 Tools/test_trajectory_calculation.py` to confirm planner math.
- Validate navigation loops in Gazebo: launch the simulator, then the main stack, watching `/navi_result`, `/cmd_vel`, `/vehicle/lateral_deviation`.
- Use `ros2 bag record /state_estimation /cmd_vel` when gathering evidence for regressions and replay with analysis scripts in `Tools/`.
- Keep waypoint fixtures in `data/` and `src/global_traj_generate/data/` stable; document intentional changes in PR descriptions.

## Commit & Pull Request Guidelines
- Mimic existing short action-first commit messages (see `git log`), often imperative Chinese such as `修复启动后导航超调问题`.
- Split features and fixes into separate commits; mention touched packages (`global_traj_generate`, `differential_tracked`) explicitly.
- Pull requests must summarize behavior shifts, list the launch/test commands executed, and attach RViz screenshots or logs when visual output changes.
- Before requesting review, ensure configuration defaults are restored and lint/build/test steps succeed locally.

## Configuration & Safety Notes
- Keep `navigator_params.yaml` and `global_traj_generate_ros2.yaml` aligned; mismatched tolerances lead to oscillations near goals.
- Update `nav.bash` only for enduring launch changes; otherwise instruct users to pass overrides through package launch files.
- Large meshes under `src/vehicle_simulator/mesh/` belong under version control as-is; add new assets via Git LFS if they exceed repository limits.
