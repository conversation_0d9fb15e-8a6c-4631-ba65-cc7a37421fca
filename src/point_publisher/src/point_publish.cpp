#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <nav_msgs/msg/path.hpp>
#include <tf2/LinearMath/Transform.h>
#include <tf2_ros/transform_broadcaster.h>
#include <sstream>
#include <fstream>
#include <vector>
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <std_msgs/msg/int8.hpp>
#include <std_msgs/msg/empty.hpp>
#include <iostream>
#include <thread>
#include <chrono>
#include <string>

using namespace std;

// 路径点数据结构  
struct WaypointData {
    double x, y, z;
    int point_id;
};

// ROS2 节点类 - 路径点发布器（根据导航-To-APP接口调整）
class PointPublishNode : public rclcpp::Node
{
public:
    PointPublishNode() : Node("point_publish_node")
    {
            // === 初始化发布者 ===
        // 根据接口要求：发布/path(nav_msgs::Path)
        pubPath = this->create_publisher<nav_msgs::msg::Path>("/path", 10);
        
        // 发布路径点状态
        pubPointStatus = this->create_publisher<std_msgs::msg::Int8>("/point_status", 10);
        
        // === 移除订阅者 - 不再监听导航控制信号 ===
        // point_publish 节点职责简化：启动后持续发布路径数据
        
        
        // === 初始化参数 ===
        this->declare_parameter("filename", std::string("/home/<USER>/data/point_record.txt"));
        cout<<"filename: "<<this->get_parameter("filename").as_string()<<endl;
        this->declare_parameter("publish_rate", 2.0);
        
        waypoints_file_ = this->get_parameter("filename").as_string();
        double publish_rate = this->get_parameter("publish_rate").as_double();
        
        
        // 预加载路径点数据
        loadWaypoints();
        
        // 创建定时器持续发布路径数据
        publish_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(static_cast<int>(1000.0 / publish_rate)),
            std::bind(&PointPublishNode::publishTimerCallback, this)
        );
        
        RCLCPP_INFO(this->get_logger(), 
                   "路径点发布器初始化完成 - 持续发布路径数据，加载了 %zu 个路径点", 
                   waypoints_.size());
    }

private:
    // === ROS接口 ===
    rclcpp::Publisher<nav_msgs::msg::Path>::SharedPtr pubPath;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr pubPointStatus;
    rclcpp::TimerBase::SharedPtr publish_timer_;
    
    // === 状态变量 ===
    std::vector<WaypointData> waypoints_;
    std::string waypoints_file_;
    bool waypoints_loaded_successfully_ = false;
    
    void publishTimerCallback()
    {
        // 首先发布路径点读取状态 (0:读取成功，1:读取失败)
        std_msgs::msg::Int8 status_msg;
        status_msg.data = waypoints_loaded_successfully_ ? 0 : 1;
        pubPointStatus->publish(status_msg);
        
        // 只有读取成功后才发布路径数据
        if (waypoints_loaded_successfully_ && !waypoints_.empty()) {
            publishAllWaypoints();
        }
    }
    
    void publishAllWaypoints()
    {
        // 根据接口要求：发布/path(nav_msgs::Path)包含所有路径点
        nav_msgs::msg::Path path_msg;
        path_msg.header.stamp = this->get_clock()->now();
        path_msg.header.frame_id = "map";
        
        // 添加所有路径点到路径消息中
        for (size_t i = 0; i < waypoints_.size(); ++i) {
            const auto& waypoint = waypoints_[i];
            
            geometry_msgs::msg::PoseStamped pose_stamped;
            pose_stamped.header = path_msg.header;
            
            // 设置位置
            pose_stamped.pose.position.x = waypoint.x;
            pose_stamped.pose.position.y = waypoint.y;
            pose_stamped.pose.position.z = waypoint.z;
            
            // 设置朝向（指向下一个路径点，如果有的话）
            if (i + 1 < waypoints_.size()) {
                const auto& next_waypoint = waypoints_[i + 1];
                double dx = next_waypoint.x - waypoint.x;
                double dy = next_waypoint.y - waypoint.y;
                double yaw = atan2(dy, dx);
                
                // 将偏航角转换为四元数
                pose_stamped.pose.orientation.w = cos(yaw / 2);
                pose_stamped.pose.orientation.z = sin(yaw / 2);
                pose_stamped.pose.orientation.x = 0.0;
                pose_stamped.pose.orientation.y = 0.0;
            } else {
                // 最后一个点，保持默认朝向
                pose_stamped.pose.orientation.w = 1.0;
                pose_stamped.pose.orientation.x = 0.0;
                pose_stamped.pose.orientation.y = 0.0;
                pose_stamped.pose.orientation.z = 0.0;
            }
            
            path_msg.poses.push_back(pose_stamped);
        }
        
        pubPath->publish(path_msg);
        
        RCLCPP_DEBUG(this->get_logger(), 
                   "发布完整路径 - %zu 个路径点", waypoints_.size());
    }
    
    void loadWaypoints()
    {
        waypoints_.clear();
        waypoints_loaded_successfully_ = false;
        
        std::ifstream file(waypoints_file_);
        
        if (!file.is_open()) {
            RCLCPP_ERROR(this->get_logger(), "无法打开路径点文件: %s，状态设置为读取失败", waypoints_file_.c_str());
            return;
        }

        std::string line;
        int point_id = 0;
        
        while (std::getline(file, line)) {
            // 跳过空行和注释行
            if (line.empty() || line[0] == '#') {
                continue;
            }
            
            // 解析坐标（支持格式：x y z、x y 或 x,y,z、x,y）
            WaypointData wp;
            bool parsed = false;
            
            try {
                if (line.find(',') != std::string::npos) {
                    // 逗号分隔格式
                    std::istringstream iss(line);
                    std::string token;
                    std::vector<std::string> tokens;
                    
                    while (std::getline(iss, token, ',')) {
                        // 去除空格
                        token.erase(0, token.find_first_not_of(" \t"));
                        token.erase(token.find_last_not_of(" \t") + 1);
                        tokens.push_back(token);
                    }
                    
                    if (tokens.size() >= 2) {
                        wp.x = std::stod(tokens[0]);
                        wp.y = std::stod(tokens[1]);
                        wp.z = (tokens.size() > 2) ? std::stod(tokens[2]) : 0.0;
                        parsed = true;
                    }
                } else {
                    // 空格分隔格式
                    std::istringstream iss(line);
                    if (iss >> wp.x >> wp.y) {
                        if (!(iss >> wp.z)) {
                            wp.z = 0.0;  // 如果没有z值，默认为0
                        }
                        parsed = true;
                    }
                }
                
                if (parsed) {
                    wp.point_id = point_id++;
                    waypoints_.push_back(wp);
                    
                    RCLCPP_DEBUG(this->get_logger(), "加载路径点 %d: (%.2f, %.2f, %.2f)", 
                               wp.point_id, wp.x, wp.y, wp.z);
                }
            } catch (const std::exception& e) {
                RCLCPP_WARN(this->get_logger(), "解析路径点失败: %s", line.c_str());
            }
        }
        
        file.close();
        
        // 设置读取状态
        waypoints_loaded_successfully_ = !waypoints_.empty();
        
        if (waypoints_loaded_successfully_) {
            RCLCPP_INFO(this->get_logger(), "从文件 %s 成功加载了 %zu 个路径点，状态设置为读取成功", 
                       waypoints_file_.c_str(), waypoints_.size());
        } else {
            RCLCPP_WARN(this->get_logger(), "文件 %s 没有有效的路径点数据，状态设置为读取失败", 
                       waypoints_file_.c_str());
        }
    }
    
};

// Temporary simple main function for compilation
int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<PointPublishNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}