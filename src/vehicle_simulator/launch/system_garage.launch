import os

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource, FrontendLaunchDescriptionSource
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration 

def generate_launch_description():
  world_name = LaunchConfiguration('world_name')
  vehicleHeight = LaunchConfiguration('vehicleHeight')
  cameraOffsetZ = LaunchConfiguration('cameraOffsetZ')
  vehicleX = LaunchConfiguration('vehicleX')
  vehicleY = LaunchConfiguration('vehicleY')
  vehicleZ = LaunchConfiguration('vehicleZ')
  terrainZ = LaunchConfiguration('terrainZ')
  vehicleYaw = LaunchConfiguration('vehicleYaw')
  gazebo_gui = LaunchConfiguration('gazebo_gui')
  checkTerrainConn = LaunchConfiguration('checkTerrainConn')
  use_cpp_navigator = LaunchConfiguration('use_cpp_navigator')
  
  declare_world_name = DeclareLaunchArgument('world_name', default_value='garage', description='')
  declare_vehicleHeight = DeclareLaunchArgument('vehicleHeight', default_value='0.75', description='')
  declare_cameraOffsetZ = DeclareLaunchArgument('cameraOffsetZ', default_value='0.0', description='')
  declare_vehicleX = DeclareLaunchArgument('vehicleX', default_value='0.0', description='')
  declare_vehicleY = DeclareLaunchArgument('vehicleY', default_value='0.0', description='')
  declare_vehicleZ = DeclareLaunchArgument('vehicleZ', default_value='0.0', description='')
  declare_terrainZ = DeclareLaunchArgument('terrainZ', default_value='0.0', description='')
  declare_vehicleYaw = DeclareLaunchArgument('vehicleYaw', default_value='0.0', description='')
  declare_gazebo_gui = DeclareLaunchArgument('gazebo_gui', default_value='true', description='')
  declare_checkTerrainConn = DeclareLaunchArgument('checkTerrainConn', default_value='true', description='')
  declare_use_cpp_navigator = DeclareLaunchArgument('use_cpp_navigator', default_value='false', description='是否使用C++版本的导航器 (true: C++版本, false: Python版本)')
  
  # 启动导航模块 - 替换原来的local_planner等模块
  start_navigation = IncludeLaunchDescription(
    PythonLaunchDescriptionSource(os.path.join(
      get_package_share_directory('differential_tracked'), 'launch', 'diff_tracked_system.launch.py')
    ),
    launch_arguments={
      'use_sim_time': 'true',
      'use_cpp_navigator': use_cpp_navigator,
      'enable_point_publisher': 'false',  # 使用下面单独启动的点发布器
      'enable_rviz': 'false',  # 使用下面单独启动的RViz
    }.items()
  )

  # 启动点发布器
  point_publisher_pkg = get_package_share_directory('point_publisher')
  point_publisher_config_file = os.path.join(point_publisher_pkg, 'config', 'point_publish.yaml')
  start_point_publisher = Node(
    package='point_publisher',
    executable='point_publish',
    name='point_publish_node',
    output='screen',
    parameters=[
      point_publisher_config_file,
      {'use_sim_time': True}
    ]
  )

  start_vehicle_simulator = IncludeLaunchDescription(
    PythonLaunchDescriptionSource(os.path.join(
      get_package_share_directory('vehicle_simulator'), 'launch', 'vehicle_simulator.launch')
    ),
    launch_arguments={
      'world_name': world_name,
      'vehicleHeight': vehicleHeight,
      'cameraOffsetZ': cameraOffsetZ,
      'vehicleX': vehicleX,
      'vehicleY': vehicleY,
      'terrainZ': terrainZ,
      'vehicleYaw': vehicleYaw,
      'gui': gazebo_gui,
      'use_sim_time': 'true',
    }.items()
  )

  # 启用可视化工具
  start_visualization_tools = IncludeLaunchDescription(
    FrontendLaunchDescriptionSource(os.path.join(
      get_package_share_directory('visualization_tools'), 'launch', 'visualization_tools.launch')
    ),
    launch_arguments={
      'world_name': world_name,
    }.items()
  )

  start_joy = Node(
    package='joy', 
    executable='joy_node',
    name='ps3_joy',
    output='screen',
    parameters=[{
                'dev': "/dev/input/js0",
                'deadzone': 0.12,
                'autorepeat_rate': 0.0,
  		}]
  )

  rviz_config_file = os.path.join(get_package_share_directory('vehicle_simulator'), 'rviz', 'vehicle_simulator.rviz')
  start_rviz = Node(
    package='rviz2',
    executable='rviz2',
    arguments=['-d', rviz_config_file],
    output='screen'
  )

  delayed_start_rviz = TimerAction(
    period=8.0,
    actions=[
      start_rviz
    ]
  )

  ld = LaunchDescription()

  # Add the actions
  ld.add_action(declare_world_name)
  ld.add_action(declare_vehicleHeight)
  ld.add_action(declare_cameraOffsetZ)
  ld.add_action(declare_vehicleX)
  ld.add_action(declare_vehicleY)
  ld.add_action(declare_vehicleZ)
  ld.add_action(declare_terrainZ)
  ld.add_action(declare_vehicleYaw)
  ld.add_action(declare_gazebo_gui)
  ld.add_action(declare_checkTerrainConn)
  ld.add_action(declare_use_cpp_navigator)

  ld.add_action(start_navigation)
  ld.add_action(start_point_publisher)
  ld.add_action(start_vehicle_simulator)
  #ld.add_action(start_visualization_tools)
  ld.add_action(start_joy)
  ld.add_action(delayed_start_rviz)

  return ld
