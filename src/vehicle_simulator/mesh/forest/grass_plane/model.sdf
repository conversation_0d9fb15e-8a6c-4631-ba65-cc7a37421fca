<?xml version='1.0'?>
<sdf version='1.4'>
  <model name='grasspatch'>
    <static>true</static>
    <link name='link'>
      <collision name='collision'>
        <pose>0 0 0 0 0 0</pose>  
        <geometry>
          <plane>
            <normal>0 0 1</normal>
            <size>150 150</size>
          </plane>
        </geometry>
        <surface>
          <friction>
            <ode>
              <mu>0.5</mu>
              <mu2>.5</mu2>
            </ode>
          </friction>
        </surface>
      </collision>

      <visual name='visual_s10'>
        <pose>0 0 0 0 0 0</pose>
        <cast_shadows>false</cast_shadows>
        <geometry>
          <plane>
            <normal>0 0 1</normal>
            <size>150 150</size>
          </plane>
        </geometry>
          <material>
          <diffuse>1.0 1.0 1.0</diffuse>
          <specular>1.0 1.0 1.0</specular>
        <pbr>
        <metal>
          <albedo_map>materials/textures/Grass_Albedo.jpg</albedo_map>
          <normal>materials/textures/flat_normal.png</normal>
        </metal>
        </pbr>
        </material>
      </visual>
    </link>
  </model>
</sdf>

