#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/empty.hpp>
#include <std_msgs/msg/int8.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include <Eigen/Dense>

#include <sensor_msgs/msg/point_cloud2.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/quaternion.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>

#include "global_traj_generate/msg/navigation_result.hpp"
#include "global_traj_generate/msg/navigation_target.hpp"
#include "global_traj_generate/msg/lateral_deviation.hpp"

#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <limits>
#include <cmath>

using namespace std;
constexpr double PI = 3.14159265;

// 全局轨迹生成器类（根据导航-To-APP接口调整）
class GlobalTrajGenerate : public rclcpp::Node
{
public:
    GlobalTrajGenerate() : Node("global_traj_generate")
    {
        // === 初始化订阅者 ===
        // 根据接口要求：接收/path获取路径点
        sub_path_ = this->create_subscription<nav_msgs::msg::Path>(
            "/path", 10, std::bind(&GlobalTrajGenerate::pathCallback, this, std::placeholders::_1));
        
        // 根据接口要求：接收/start_navigation启动导航
        sub_start_navigation_ = this->create_subscription<std_msgs::msg::Int8>(
            "/start_navigation", 10, std::bind(&GlobalTrajGenerate::startNavigationCallback, this, std::placeholders::_1));
        
        // 根据接口要求：接收/stop_navigation停止导航
        sub_stop_navigation_ = this->create_subscription<std_msgs::msg::Int8>(
            "/stop_navigation", 10, std::bind(&GlobalTrajGenerate::stopNavigationCallback, this, std::placeholders::_1));
        
        // 接收里程计数据获取当前位置
        sub_laser_odometry_ = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 10, std::bind(&GlobalTrajGenerate::laserOdometryCallback, this, std::placeholders::_1));
        
        // 接收暂停导航指令
        sub_pause_navigation_ = this->create_subscription<std_msgs::msg::Int8>(
            "/pause_navigation", 10, std::bind(&GlobalTrajGenerate::pauseNavigationCallback, this, std::placeholders::_1));
        
        // 接收恢复导航指令
        sub_resume_navigation_ = this->create_subscription<std_msgs::msg::Int8>(
            "/resume_navigation", 10, std::bind(&GlobalTrajGenerate::resumeNavigationCallback, this, std::placeholders::_1));
        
        // === 初始化发布者 ===
        // 根据接口要求：发布/local_goal(geometry_msgs::PoseStamped)
        pub_local_goal_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/local_goal", 10);
        
        // 根据接口要求：发布/stopAtTarget当车辆到达目的地
        pub_stop_at_target_ = this->create_publisher<std_msgs::msg::Int8>("/stopAtTarget", 10);
        
        // 横向偏移监控发布者
        pub_lateral_deviation_ = this->create_publisher<global_traj_generate::msg::LateralDeviation>("/vehicle/lateral_deviation", 10);
        
        // 初始导航点状态发布者
        pub_initial_first_point_ = this->create_publisher<std_msgs::msg::Int8>("/initial_first_point", 10);
        
        // 订阅路径点状态
        sub_point_status_ = this->create_subscription<std_msgs::msg::Int8>(
            "/point_status", 10, std::bind(&GlobalTrajGenerate::pointStatusCallback, this, std::placeholders::_1));
        
        // === 初始化参数 ===
        this->declare_parameter("goal_tolerance", 0.3);
        this->declare_parameter("trajectory_update_rate", 10.0);
        this->declare_parameter("lookahead_distance", 1.0);
        this->declare_parameter("lateral_deviation_threshold", 10.0);
        this->declare_parameter("distance_first_point", 5.0);
        this->declare_parameter("angle_first_point", 60.0);
        this->declare_parameter("enable_initial_point_check", true);
        
        goal_tolerance_ = this->get_parameter("goal_tolerance").as_double();
        double update_rate = this->get_parameter("trajectory_update_rate").as_double();
        lookahead_distance_ = this->get_parameter("lookahead_distance").as_double();
        lateral_deviation_threshold_ = this->get_parameter("lateral_deviation_threshold").as_double();
        distance_first_point_ = this->get_parameter("distance_first_point").as_double();
        angle_first_point_ = this->get_parameter("angle_first_point").as_double();
        enable_initial_point_check_ = this->get_parameter("enable_initial_point_check").as_bool();
        
        // === 初始化状态变量 ===
        navigation_active_ = false;
        navigation_paused_ = false;
        current_target_reached_ = false;
        path_points_loaded_ = false;
        is_at_initial_first_point_ = false;
        
        // 创建定时器用于定期检查和发布局部目标
        trajectory_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(static_cast<int>(1000.0 / update_rate)),
            std::bind(&GlobalTrajGenerate::trajectoryUpdateCallback, this)
        );
        
        // 创建初始导航点检查定时器
        initial_point_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(200),  // 5Hz检查频率
            std::bind(&GlobalTrajGenerate::checkInitialFirstPoint, this)
        );
        
        RCLCPP_INFO(this->get_logger(), 
                   "全局轨迹生成器初始化完成 - 等待/path路径点数据，目标容差: %.2fm，横向偏移阈值: %.2fm，初始点距离阈值: %.2fm，初始点角度阈值: %.2f°，初始点检查: %s", 
                   goal_tolerance_, lateral_deviation_threshold_, distance_first_point_, angle_first_point_, 
                   enable_initial_point_check_ ? "启用" : "禁用");
    }

private:
    // === ROS接口 ===
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr sub_path_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_start_navigation_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_stop_navigation_;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr sub_laser_odometry_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_pause_navigation_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_resume_navigation_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_point_status_;
    
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pub_local_goal_;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr pub_stop_at_target_;
    rclcpp::Publisher<global_traj_generate::msg::LateralDeviation>::SharedPtr pub_lateral_deviation_;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr pub_initial_first_point_;
    
    rclcpp::TimerBase::SharedPtr trajectory_timer_;
    rclcpp::TimerBase::SharedPtr initial_point_timer_;
    
    // === 状态变量 ===
    nav_msgs::msg::Odometry current_odom_;
    geometry_msgs::msg::PoseStamped current_target_;
    nav_msgs::msg::Path current_path_;  // 存储完整路径
    size_t current_waypoint_index_ = 0;  // 当前路径点索引
    bool navigation_active_;
    bool navigation_paused_;
    bool current_target_reached_;
    bool has_current_odom_ = false;
    bool has_current_target_ = false;
    bool path_points_loaded_ = false;
    bool is_at_initial_first_point_ = false;
    
    // === 参数 ===
    double goal_tolerance_;
    double lookahead_distance_;
    double lateral_deviation_threshold_;
    double distance_first_point_;
    double angle_first_point_;
    bool enable_initial_point_check_;
    
    void pathCallback(const nav_msgs::msg::Path::SharedPtr msg)
    {
        // 根据接口要求：接收/path路径点数据进行顺序导航
        if (msg->poses.empty()) {
            RCLCPP_WARN(this->get_logger(), "接收到空的路径数据");
            return;
        }
        
        // 检查是否为相同路径，避免重复重置导航进度
        bool is_same_path = false;
        if (!current_path_.poses.empty() && current_path_.poses.size() == msg->poses.size()) {
            is_same_path = true;
            for (size_t i = 0; i < msg->poses.size(); i++) {
                const auto& old_pose = current_path_.poses[i].pose;
                const auto& new_pose = msg->poses[i].pose;
                double dx = old_pose.position.x - new_pose.position.x;
                double dy = old_pose.position.y - new_pose.position.y;
                if (sqrt(dx*dx + dy*dy) > 0.01) { // 1cm tolerance
                    is_same_path = false;
                    break;
                }
            }
        }
        
        if (is_same_path) {
            RCLCPP_DEBUG(this->get_logger(), "收到相同路径，保持当前导航进度");
            return;
        }
        
        // 存储新路径
        current_path_ = *msg;
        
        // 不再在此处自动启动导航，等待start_navigation命令
        RCLCPP_INFO(this->get_logger(), 
                   "收到新路径数据 - 共 %zu 个路径点，等待/start_navigation命令启动导航", 
                   msg->poses.size());
    }
    
    void pointStatusCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 接收路径点加载状态
        path_points_loaded_ = (msg->data == 0);
    }
    
    void startNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        
        // 根据接口要求：接收/start_navigation启动或重启导航
        if (msg->data != 1) {
            RCLCPP_INFO(this->get_logger(), "启动导航信号值不为1，忽略");
            return;  // 只响应值为1的命令
        }
        
        // 检查路径点是否加载成功
        if (!path_points_loaded_) {
            RCLCPP_WARN(this->get_logger(), "路径点未成功加载，无法启动导航");
            return;
        }
        
        // 检查是否在初始导航点附近
        if (enable_initial_point_check_ && !is_at_initial_first_point_) {
            RCLCPP_WARN(this->get_logger(), "车辆不在初始导航点附近，无法启动导航");
            return;
        }
        
        // 清除暂停状态
        navigation_paused_ = false;
        
        if (!current_path_.poses.empty()) {
            // 如果导航未激活（停止状态），从第一个路径点开始
            if (!navigation_active_) {
                // 再次确认车辆在初始导航点附近（防止状态不同步导致的bug）
                if (enable_initial_point_check_ && !is_at_initial_first_point_) {
                    RCLCPP_WARN(this->get_logger(), "启动导航时检测到车辆不在初始导航点附近，无法启动导航");
                    return;
                }
                
                // 从头开始导航
                current_waypoint_index_ = 0;
                current_target_ = current_path_.poses[0];
                has_current_target_ = true;
                current_target_reached_ = false;
                navigation_active_ = true;
                
                RCLCPP_INFO(this->get_logger(), 
                           "启动导航成功！从第一个路径点开始: (%.2f, %.2f)", 
                           current_target_.pose.position.x, current_target_.pose.position.y);
                
                // 立即发布局部目标
                publishLocalGoal();
            } else {
                // 导航已激活，继续当前导航
                RCLCPP_INFO(this->get_logger(), 
                           "导航继续 - 当前路径点 %zu/%zu: (%.2f, %.2f)", 
                           current_waypoint_index_ + 1, current_path_.poses.size(),
                           current_target_.pose.position.x, current_target_.pose.position.y);
                
                // 确保继续发布局部目标
                publishLocalGoal();
            }
        } else {
            RCLCPP_WARN(this->get_logger(), "收到启动导航信号，但无可用路径点数据");
        }
    }
    
    size_t findNearestWaypoint()
    {
        // 基于当前位置找到最合适的前方路径点（避免掉头问题）
        if (!has_current_odom_ || current_path_.poses.empty()) {
            return 0;
        }
        
        double current_x = current_odom_.pose.pose.position.x;
        double current_y = current_odom_.pose.pose.position.y;
        
        if (current_path_.poses.size() == 1) {
            return 0;
        }
        
        double min_distance = std::numeric_limits<double>::max();
        size_t best_index = 0;
        double best_projection_param = 0.0;
        
        // 遍历路径中的每个线段
        for (size_t i = 0; i < current_path_.poses.size() - 1; i++) {
            double x1 = current_path_.poses[i].pose.position.x;
            double y1 = current_path_.poses[i].pose.position.y;
            double x2 = current_path_.poses[i + 1].pose.position.x;
            double y2 = current_path_.poses[i + 1].pose.position.y;
            
            // 计算点到线段的投影
            auto [distance, projection_param] = calculatePointToSegmentProjection(
                current_x, current_y, x1, y1, x2, y2);
            
            if (distance < min_distance) {
                min_distance = distance;
                best_index = i;
                best_projection_param = projection_param;
            }
        }
        
        // 根据投影参数选择合适的路径点索引
        size_t target_index;
        if (best_projection_param <= 0.0) {
            // 投影在线段起点之前，选择起点
            target_index = best_index;
        } else if (best_projection_param >= 1.0) {
            // 投影在线段终点之后，选择终点的下一个点（如果存在）
            target_index = std::min(best_index + 1, current_path_.poses.size() - 1);
        } else {
            // 投影在线段内，选择线段终点
            target_index = best_index + 1;
        }
        
        // 额外安全检查：如果选择的点比当前索引靠后太多，逐步前进
        if (navigation_active_ && target_index > current_waypoint_index_ + 3) {
            target_index = current_waypoint_index_ + 1;
            if (target_index >= current_path_.poses.size()) {
                target_index = current_path_.poses.size() - 1;
            }
        }
        
        RCLCPP_INFO(this->get_logger(), 
                   "当前位置: (%.2f, %.2f), 最近线段: %zu->%zu, 投影参数: %.3f, 选择路径点: %zu, 距离: %.2fm", 
                   current_x, current_y, best_index + 1, best_index + 2, 
                   best_projection_param, target_index + 1, min_distance);
        
        return target_index;
    }
    
    // 计算点到线段的投影距离和投影参数
    std::pair<double, double> calculatePointToSegmentProjection(
        double px, double py, double x1, double y1, double x2, double y2)
    {
        // 线段向量
        double dx = x2 - x1;
        double dy = y2 - y1;
        
        // 线段长度的平方
        double segment_length_sq = dx * dx + dy * dy;
        
        if (segment_length_sq < 1e-8) {
            // 线段退化为点
            double dist = sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1));
            return {dist, 0.0};
        }
        
        // 计算投影参数 t
        double t = ((px - x1) * dx + (py - y1) * dy) / segment_length_sq;
        
        // 计算投影点
        double proj_x, proj_y;
        if (t <= 0.0) {
            // 投影在线段起点之前
            proj_x = x1;
            proj_y = y1;
        } else if (t >= 1.0) {
            // 投影在线段终点之后
            proj_x = x2;
            proj_y = y2;
        } else {
            // 投影在线段内
            proj_x = x1 + t * dx;
            proj_y = y1 + t * dy;
        }
        
        // 计算距离
        double distance = sqrt((px - proj_x) * (px - proj_x) + (py - proj_y) * (py - proj_y));
        
        return {distance, t};
    }
    
    void stopNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 根据接口要求：接收/stop_navigation停止发布导航数据
        if (msg->data != 1) {
            return;  // 只响应值为1的命令
        }
        
        // 停止导航（简化的状态管理）
        
        if (navigation_active_ || navigation_paused_) {
            navigation_active_ = false;
            navigation_paused_ = false;
            has_current_target_ = false;
            
            RCLCPP_INFO(this->get_logger(), "收到停止导航信号 - 停止发布导航数据");
        } else {
            RCLCPP_INFO(this->get_logger(), "收到停止导航信号，但导航未激活");
        }
    }
    
    void laserOdometryCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        current_odom_ = *msg;
        has_current_odom_ = true;
    }
    
    void pauseNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 接收/pause_navigation暂停导航
        if (msg->data != 1) {
            return;  // 只响应值为1的命令
        }
        
        if (navigation_active_ && !navigation_paused_) {
            navigation_paused_ = true;
            RCLCPP_INFO(this->get_logger(), 
                       "收到暂停导航信号 - 暂停在路径点 %zu/%zu: (%.2f, %.2f)", 
                       current_waypoint_index_ + 1, current_path_.poses.size(),
                       current_target_.pose.position.x, current_target_.pose.position.y);
        } else if (navigation_paused_) {
            RCLCPP_INFO(this->get_logger(), "收到暂停导航信号，但导航已经暂停");
        } else {
            RCLCPP_INFO(this->get_logger(), "收到暂停导航信号，但导航未激活");
        }
    }
    
    void resumeNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 接收/resume_navigation恢复导航
        if (msg->data != 1) {
            return;  // 只响应值为1的命令
        }
        
        if (navigation_active_ && navigation_paused_) {
            navigation_paused_ = false;
            
            // 智能路径点重选：避免在resume时出现掉头行驶
            if (has_current_odom_ && !current_path_.poses.empty()) {
                size_t optimal_index = findNearestWaypoint();
                
                if (optimal_index != current_waypoint_index_) {
                    RCLCPP_INFO(this->get_logger(), 
                               "检测到位置变化，调整路径点: %zu -> %zu 避免掉头行驶", 
                               current_waypoint_index_ + 1, optimal_index + 1);
                    
                    current_waypoint_index_ = optimal_index;
                    current_target_ = current_path_.poses[current_waypoint_index_];
                    current_target_reached_ = false;  // 重置到达状态
                    has_current_target_ = true;
                }
            }
            
            // 恢复发布当前目标点
            if (has_current_target_) {
                RCLCPP_INFO(this->get_logger(), 
                           "收到恢复导航信号 - 从路径点 %zu/%zu继续: (%.2f, %.2f)", 
                           current_waypoint_index_ + 1, current_path_.poses.size(),
                           current_target_.pose.position.x, current_target_.pose.position.y);
                
                // 立即发布局部目标
                publishLocalGoal();
            }
        } else if (!navigation_paused_) {
            RCLCPP_INFO(this->get_logger(), "收到恢复导航信号，但导航未暂停");
        } else {
            RCLCPP_INFO(this->get_logger(), "收到恢复导航信号，但导航未激活");
        }
    }

    void trajectoryUpdateCallback()
    {
        // 检查导航状态和必要条件
        if (!navigation_active_ || !has_current_target_ || !has_current_odom_) {
            return;
        }
        
        // 如果导航暂停，不更新目标点
        if (navigation_paused_) {
            return;
        }
        
        // 检查是否到达目标点
        if (hasReachedTarget()) {
            if (!current_target_reached_) {
                // 刚到达当前目标点
                current_target_reached_ = true;
                
                RCLCPP_INFO(this->get_logger(), 
                           "已到达路径点 %zu/%zu: (%.2f, %.2f)", 
                           current_waypoint_index_ + 1, current_path_.poses.size(),
                           current_target_.pose.position.x, current_target_.pose.position.y);
                
                // 检查是否还有下一个路径点
                if (current_waypoint_index_ + 1 < current_path_.poses.size()) {
                    // 切换到下一个路径点
                    current_waypoint_index_++;
                    current_target_ = current_path_.poses[current_waypoint_index_];
                    current_target_reached_ = false;
                    
                    // 检查新目标是否过于接近当前位置，如果是则跳过
                    if (has_current_odom_) {
                        double dx = current_target_.pose.position.x - current_odom_.pose.pose.position.x;
                        double dy = current_target_.pose.position.y - current_odom_.pose.pose.position.y;
                        double distance = sqrt(dx * dx + dy * dy);
                        
                        if (distance < goal_tolerance_) {
                            RCLCPP_WARN(this->get_logger(), 
                                       "路径点 %zu 过于接近当前位置 (%.3fm < %.3fm)，跳过到下一个路径点", 
                                       current_waypoint_index_ + 1, distance, goal_tolerance_);
                            return;  // 让下次定时器回调处理跳过
                        }
                    }
                    
                    RCLCPP_INFO(this->get_logger(), 
                               "切换到下一个路径点 %zu/%zu: (%.2f, %.2f)", 
                               current_waypoint_index_ + 1, current_path_.poses.size(),
                               current_target_.pose.position.x, current_target_.pose.position.y);
                    
                    // 发布新的局部目标
                    publishLocalGoal();
                } else {
                    // 所有路径点都已到达
                    std_msgs::msg::Int8 stop_msg;
                    stop_msg.data = 1;  // 1表示到达目标停车
                    pub_stop_at_target_->publish(stop_msg);
                    
                    RCLCPP_INFO(this->get_logger(), 
                               "所有路径点导航完成！总共 %zu 个路径点", 
                               current_path_.poses.size());
                    
                    // 停止导航
                    navigation_active_ = false;
                    return;
                }
            }
        } else {
            // 未到达目标，继续发布局部目标
            current_target_reached_ = false;
            publishLocalGoal();
            
            // 检查横向偏移
            checkLateralDeviation();
        }
    }

    bool hasReachedTarget()
    {
        if (!has_current_target_ || !has_current_odom_) {
            return false;
        }

        double dx = current_target_.pose.position.x - current_odom_.pose.pose.position.x;
        double dy = current_target_.pose.position.y - current_odom_.pose.pose.position.y;
        double distance = sqrt(dx * dx + dy * dy);
        
        return distance < goal_tolerance_;
    }
    
    // 计算四元数到yaw角的转换
    double getYawFromQuaternion(const geometry_msgs::msg::Quaternion& q)
    {
        double siny_cosp = 2.0 * (q.w * q.z + q.x * q.y);
        double cosy_cosp = 1.0 - 2.0 * (q.y * q.y + q.z * q.z);
        return std::atan2(siny_cosp, cosy_cosp);
    }
    
    void checkLateralDeviation()
    {
        // 检查横向偏移（使用叉积方法计算真正的横向偏差）
        if (!has_current_target_ || !has_current_odom_ || current_path_.poses.empty()) {
            return;
        }
        
        // 当前车辆位置和朝向
        double x = current_odom_.pose.pose.position.x;
        double y = current_odom_.pose.pose.position.y;
        double theta = getYawFromQuaternion(current_odom_.pose.pose.orientation);
        
        // 找到最近的轨迹点
        double min_dist = std::numeric_limits<double>::max();
        int nearest_idx = 0;
        for (size_t i = 0; i < current_path_.poses.size(); i++) {
            double dx = x - current_path_.poses[i].pose.position.x;
            double dy = y - current_path_.poses[i].pose.position.y;
            double dist = dx * dx + dy * dy;
            if (dist < min_dist) {
                min_dist = dist;
                nearest_idx = i;
            }
        }
        
        auto nearest_point = current_path_.poses[nearest_idx].pose.position;
        
        // 计算参考方向（通过下一个点近似）
        int next_idx = std::min(nearest_idx + 1, (int)current_path_.poses.size() - 1);
        auto next_point = current_path_.poses[next_idx].pose.position;
        double theta_ref = std::atan2(next_point.y - nearest_point.y, next_point.x - nearest_point.x);
        
        // 横向偏移 e_y (叉积方法)
        double dx = x - nearest_point.x;
        double dy = y - nearest_point.y;
        double lateral_error = dx * std::sin(theta_ref) - dy * std::cos(theta_ref);
        
        // 航向误差 e_theta
        double heading_error = theta - theta_ref;
        // wrap 到 [-pi, pi]
        while (heading_error > PI) heading_error -= 2.0 * PI;
        while (heading_error < -PI) heading_error += 2.0 * PI;
        
        // 横向偏移的绝对值用于阈值判断
        double abs_lateral_error = std::abs(lateral_error);
        
        // 检查是否超过横向偏移阈值
        if (abs_lateral_error > lateral_deviation_threshold_) {
            // 发布横向偏移信号
            global_traj_generate::msg::LateralDeviation deviation_msg;
            deviation_msg.reference_path_id = std::to_string(nearest_idx);
            deviation_msg.lateral_error_m = static_cast<float>(lateral_error);
            deviation_msg.heading_error_rad = static_cast<float>(heading_error);
            pub_lateral_deviation_->publish(deviation_msg);
          /*  
            RCLCPP_WARN(this->get_logger(),
                       "检测到横向偏移过大: %.3fm (阈值: %.2fm) 航向误差: %.3frad - "
                       "当前位置: (%.2f, %.2f) 最近轨迹点: (%.2f, %.2f) 索引: %d",
                       lateral_error, lateral_deviation_threshold_, heading_error,
                       x, y, nearest_point.x, nearest_point.y, nearest_idx);
          */             
        }
    }
    
    void publishLocalGoal()
    {
        if (!has_current_target_) {
            RCLCPP_WARN(this->get_logger(), "publishLocalGoal调用失败：has_current_target_为false");
            return;
        }
        
        // 根据接口要求：发布/local_goal(geometry_msgs::PoseStamped)
        geometry_msgs::msg::PoseStamped local_goal = current_target_;
        
        // 更新时间戳和frame_id
        local_goal.header.stamp = this->get_clock()->now();
        local_goal.header.frame_id = "map";
        
        pub_local_goal_->publish(local_goal);
    }
    
    void checkInitialFirstPoint()
    {
        // 检查当前车辆位置是否在第一个导航点附近
        bool previous_state = is_at_initial_first_point_;
        
        if (!has_current_odom_ || current_path_.poses.empty() || current_path_.poses.size() < 2) {
            is_at_initial_first_point_ = false;
        } else {
            // 当前车辆位置
            double current_x = current_odom_.pose.pose.position.x;
            double current_y = current_odom_.pose.pose.position.y;
            double current_yaw = getYawFromQuaternion(current_odom_.pose.pose.orientation);
            
            // 第一个导航点
            double first_x = current_path_.poses[0].pose.position.x;
            double first_y = current_path_.poses[0].pose.position.y;
            
            // 第二个导航点
            double second_x = current_path_.poses[1].pose.position.x;
            double second_y = current_path_.poses[1].pose.position.y;
            
            // 计算到第一个导航点的距离
            double dx = current_x - first_x;
            double dy = current_y - first_y;
            double distance_to_first = sqrt(dx * dx + dy * dy);
            
            // 计算前两个导航点连线的角度
            double path_angle = atan2(second_y - first_y, second_x - first_x);
            
            // 计算角度差（弧度）
            double angle_diff = current_yaw - path_angle;
            // 将角度差规范化到[-pi, pi]
            while (angle_diff > PI) angle_diff -= 2.0 * PI;
            while (angle_diff < -PI) angle_diff += 2.0 * PI;
            
            // 转换为度数
            double angle_diff_deg = fabs(angle_diff * 180.0 / PI);
            
            // 计算车辆在第一、二导航点连线上的投影，防止在两点之间时误判为初始点
            double path_dx = second_x - first_x;
            double path_dy = second_y - first_y;
            double path_length_sq = path_dx * path_dx + path_dy * path_dy;
            
            double projection_param = 0.0;
            if (path_length_sq > 1e-8) {
                // 计算投影参数 t，t <= 0 表示在第一个点前方，t >= 1 表示在第二个点后方
                projection_param = (dx * path_dx + dy * path_dy) / path_length_sq;
            }
            
            // 判断是否满足条件
            bool distance_ok = distance_to_first <= distance_first_point_;
            bool angle_ok = angle_diff_deg <= angle_first_point_;
            // 添加投影检查：只有当车辆在第一个导航点的起始端时才认为在初始点
            bool position_ok = projection_param <= 0.0;
            
            is_at_initial_first_point_ = distance_ok && angle_ok && position_ok;
            
            RCLCPP_DEBUG(this->get_logger(), 
                        "初始点检查: 距离=%.2fm(阈值:%.2fm) 角度差=%.2f°(阈值:%.2f°) 投影参数=%.3f 状态=%s", 
                        distance_to_first, distance_first_point_, angle_diff_deg, angle_first_point_, 
                        projection_param, is_at_initial_first_point_ ? "在初始点" : "不在初始点");
        }
        
        // 发布初始导航点状态
        std_msgs::msg::Int8 initial_point_msg;
        initial_point_msg.data = is_at_initial_first_point_ ? 1 : 0;
        pub_initial_first_point_->publish(initial_point_msg);
        
        // 如果状态发生变化，记录日志
        if (previous_state != is_at_initial_first_point_) {
            RCLCPP_INFO(this->get_logger(), 
                       "初始导航点状态变化: %s -> %s", 
                       previous_state ? "在初始点" : "不在初始点",
                       is_at_initial_first_point_ ? "在初始点" : "不在初始点");
        }
    }
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<GlobalTrajGenerate>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}