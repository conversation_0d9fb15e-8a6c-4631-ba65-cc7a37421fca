cmake_minimum_required(VERSION 3.16)

# Handle system library compatibility for older jsoncpp
set(CMAKE_POLICY_VERSION_MINIMUM 3.5)

project(velodyne_gazebo_plugins)

# Set policy for FindBoost module removal
if(POLICY CMP0167)
  cmake_policy(SET CMP0167 NEW)
endif()

# Set policy version to handle system library compatibility
set(CMAKE_POLICY_DEFAULT_CMP0167 NEW)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
endif()

find_package(ament_cmake REQUIRED)
find_package(gazebo_dev REQUIRED)
find_package(gazebo_ros REQUIRED)
find_package(gazebo_msgs REQUIRED)
find_package(rclcpp REQUIRED)
find_package(sensor_msgs REQUIRED)

add_library(gazebo_ros_velodyne_laser SHARED
  src/GazeboRosVelodyneLaser.cpp
)
ament_target_dependencies(gazebo_ros_velodyne_laser
  gazebo_dev
  gazebo_ros
  gazebo_msgs
  rclcpp
  sensor_msgs
)
target_include_directories(gazebo_ros_velodyne_laser PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)

install(TARGETS
  gazebo_ros_velodyne_laser
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION lib/${PROJECT_NAME}
)
install(DIRECTORY include/${PROJECT_NAME}/
  DESTINATION include/${PROJECT_NAME}
)

ament_export_include_directories(include)
ament_export_libraries(gazebo_ros_velodyne_laser)
ament_export_dependencies(gazebo_dev)
ament_export_dependencies(gazebo_ros)
ament_export_dependencies(gazebo_msgs)
ament_export_dependencies(rclcpp)
ament_export_dependencies(sensor_msgs)
ament_package()