#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist, TwistStamped, PointStamped, PoseStamped
from nav_msgs.msg import Odometry, Path
from sensor_msgs.msg import PointCloud2
from std_msgs.msg import Empty, Int8
from global_traj_generate.msg import LateralDeviation
from rclpy.qos import QoSProfile, DurabilityPolicy, ReliabilityPolicy
import sensor_msgs_py.point_cloud2 as pc2
import numpy as np
import math
import time

class DifferentialTrackedNavigator(Node):
    def __init__(self):
        super().__init__('differential_tracked_navigator')
        self.get_logger().info('Initializing Differential Tracked Navigator Node')
        
        # 声明ROS2参数
        self._declare_parameters()
        
        # 从参数服务器加载参数
        self._load_parameters()
        
        # Robot state
        self.robot_pose = np.zeros(3)  # [x, y, theta]
        self.current_goal = None       # 从/local_goal获取的当前目标点
        self.goal_received = False
        
        # 轨迹点管理
        self.trajectory_points = []    # 轨迹点列表 [[x, y, z], ...]
        self.current_waypoint_index = 0  # 当前目标航点索引
        self.trajectory_loaded = False   # 轨迹是否已加载
        
        # 障碍物检测状态
        self.obstacle_detected = False      # 障碍物检测标志
        self.emergency_stop = False         # 紧急停车标志
        
        # 横向偏差检测状态
        self.lateral_deviation_detected = False  # 横向偏差检测标志
        
        # 导航控制状态
        self.navigation_stopped = False     # 外部停止导航标志
        self.navigation_paused = False      # 导航暂停标志

        # PID控制状态变量
        self.pid_previous_error = 0.0         # PID前一次误差
        self.pid_integral = 0.0               # PID积分累积
        self.pid_previous_time = None         # PID前一次时间戳

        # Subscribers - 适配当前系统的topics
        self.create_subscription(Odometry, '/state_estimation', self.odom_callback, 10)
        #self.create_subscription(PointCloud2, '/rslidar_points', self.lidar_callback, 10)
        self.create_subscription(PointCloud2, '/registered_scan', self.lidar_callback, 10) # 适配仿真中的topic
        self.create_subscription(PoseStamped, '/local_goal', self.goal_callback, 10)
        self.create_subscription(Int8, '/stop_navigation', self.stop_navigation_callback, 10)
        self.create_subscription(Int8, '/start_navigation', self.start_navigation_callback, 10)
        self.create_subscription(Int8, '/pause_navigation', self.pause_navigation_callback, 10)
        self.create_subscription(Int8, '/resume_navigation', self.resume_navigation_callback, 10)
        self.create_subscription(Path, '/path', self.path_callback, 10)
        self.create_subscription(LateralDeviation, '/vehicle/lateral_deviation', self.lateral_deviation_callback, 10)

        # Publishers - 适配当前系统的topics
        self.cmd_vel_publisher = self.create_publisher(Twist, '/cmd_vel', 10)
        self.waypoint_publisher = self.create_publisher(PointStamped, '/way_point', 10)
        
        # 障碍物相关发布者
        self.stop_publisher = self.create_publisher(Int8, '/stop', 10)

        self.get_logger().info('Subscriptions and Publishers initialized')
        self._print_parameter_summary()
    
    def _declare_parameters(self):
        """声明ROS2参数"""
        # 车辆物理参数
        self.declare_parameter('vehicle.track_width', 2.0)
        self.declare_parameter('vehicle.max_linear_velocity', 1.0)
        self.declare_parameter('vehicle.min_linear_velocity', 0.1)
        self.declare_parameter('vehicle.max_angular_velocity', 1.4)
        
        # Pure Pursuit控制参数
        self.declare_parameter('control.goal_tolerance', 0.3)
        self.declare_parameter('control.angle_deadzone_degrees', 2.0)
        self.declare_parameter('control.linear_velocity_factor', 0.6)
        self.declare_parameter('control.approach_slow_distance', 1.2)
        self.declare_parameter('control.smooth_stop_distance', 0.5)
        
        # Pure Pursuit距离分层控制增益
        self.declare_parameter('control.far_distance_threshold', 3.0)
        self.declare_parameter('control.far_distance_angular_gain', 2.5)
        self.declare_parameter('control.medium_distance_threshold', 1.5)
        self.declare_parameter('control.medium_distance_angular_gain', 2.0)
        self.declare_parameter('control.close_distance_threshold', 0.8)
        self.declare_parameter('control.close_distance_angular_gain', 1.5)
        self.declare_parameter('control.very_close_angular_gain', 1.0)
        self.declare_parameter('control.close_distance_factor', 0.8)
        self.declare_parameter('control.min_distance_factor', 0.5)
        
        # 障碍物检测和安全参数
        self.declare_parameter('safety.obstacle_detection_range', 2.0)
        self.declare_parameter('safety.safety_stop_distance', 0.8)
        self.declare_parameter('safety.emergency_stop_distance', 0.4)
        self.declare_parameter('safety.enable_obstacle_detection', True)
        self.declare_parameter('safety.enable_lateral_detection', True)
        self.declare_parameter('safety.obstacle_count_threshold', 30)
        self.declare_parameter('safety.yaw_filter_angle_degrees', 30.0)
        self.declare_parameter('safety.voxel_grid_size', 0.1)
        
        # PID线速度控制参数
        self.declare_parameter('control.enable_pid_linear', False)
        self.declare_parameter('control.linear_kp', 0.8)
        self.declare_parameter('control.linear_ki', 0.1)
        self.declare_parameter('control.linear_kd', 0.05)
        self.declare_parameter('control.pid_integral_limit', 2.0)
        
        # 调试和监控参数
        self.declare_parameter('debug.odometry_print_frequency', 20)
        self.declare_parameter('debug.no_goal_warning_frequency', 50)
        self.declare_parameter('debug.control_print_frequency', 10)
        self.declare_parameter('debug.obstacle_print_frequency', 5)
        self.declare_parameter('debug.trajectory_yaw_print_frequency', 20)
    
    def _load_parameters(self):
        """从参数服务器加载参数"""
        # 车辆物理参数
        self.track_width = self.get_parameter('vehicle.track_width').value
        self.max_linear_velocity = self.get_parameter('vehicle.max_linear_velocity').value
        self.min_linear_velocity = self.get_parameter('vehicle.min_linear_velocity').value
        self.max_angular_velocity = self.get_parameter('vehicle.max_angular_velocity').value
        
        # Pure Pursuit控制参数
        self.goal_tolerance = self.get_parameter('control.goal_tolerance').value
        self.angle_deadzone_degrees = self.get_parameter('control.angle_deadzone_degrees').value
        self.linear_velocity_factor = self.get_parameter('control.linear_velocity_factor').value
        self.approach_slow_distance = self.get_parameter('control.approach_slow_distance').value
        self.smooth_stop_distance = self.get_parameter('control.smooth_stop_distance').value
        
        # Pure Pursuit距离分层控制增益
        self.far_distance_threshold = self.get_parameter('control.far_distance_threshold').value
        self.far_distance_angular_gain = self.get_parameter('control.far_distance_angular_gain').value
        self.medium_distance_threshold = self.get_parameter('control.medium_distance_threshold').value
        self.medium_distance_angular_gain = self.get_parameter('control.medium_distance_angular_gain').value
        self.close_distance_threshold = self.get_parameter('control.close_distance_threshold').value
        self.close_distance_angular_gain = self.get_parameter('control.close_distance_angular_gain').value
        self.very_close_angular_gain = self.get_parameter('control.very_close_angular_gain').value
        self.close_distance_factor = self.get_parameter('control.close_distance_factor').value
        self.min_distance_factor = self.get_parameter('control.min_distance_factor').value
        
        # PID线速度控制参数
        self.enable_pid_linear = self.get_parameter('control.enable_pid_linear').value
        self.linear_kp = self.get_parameter('control.linear_kp').value
        self.linear_ki = self.get_parameter('control.linear_ki').value
        self.linear_kd = self.get_parameter('control.linear_kd').value
        self.pid_integral_limit = self.get_parameter('control.pid_integral_limit').value
        
        # 障碍物检测和安全参数
        self.obstacle_detection_range = self.get_parameter('safety.obstacle_detection_range').value
        self.safety_stop_distance = self.get_parameter('safety.safety_stop_distance').value
        self.emergency_stop_distance = self.get_parameter('safety.emergency_stop_distance').value
        self.enable_obstacle_detection = self.get_parameter('safety.enable_obstacle_detection').value
        self.enable_lateral_detection = self.get_parameter('safety.enable_lateral_detection').value
        self.obstacle_count_threshold = self.get_parameter('safety.obstacle_count_threshold').value
        self.yaw_filter_angle_degrees = self.get_parameter('safety.yaw_filter_angle_degrees').value
        self.voxel_grid_size = self.get_parameter('safety.voxel_grid_size').value
        
        # 调试和监控参数
        self.odometry_print_frequency = self.get_parameter('debug.odometry_print_frequency').value
        self.no_goal_warning_frequency = self.get_parameter('debug.no_goal_warning_frequency').value
        self.control_print_frequency = self.get_parameter('debug.control_print_frequency').value
        self.obstacle_print_frequency = self.get_parameter('debug.obstacle_print_frequency').value
        self.trajectory_yaw_print_frequency = self.get_parameter('debug.trajectory_yaw_print_frequency').value
        
        # 转换角度参数为弧度
        self.angle_deadzone = math.radians(self.angle_deadzone_degrees)
        self.yaw_filter_angle = math.radians(self.yaw_filter_angle_degrees)
        
        # 轨迹点将通过/path topic接收，无需从文件加载
    
    def _print_parameter_summary(self):
        """打印参数配置摘要"""
        self.get_logger().info('=== Differential Tracked Navigator参数配置 ===')
        self.get_logger().info(f'车辆参数: 履带宽度={self.track_width}m, 最大线速度={self.max_linear_velocity}m/s, 最小线速度={self.min_linear_velocity}m/s, 最大角速度={self.max_angular_velocity}rad/s')
        self.get_logger().info(f'控制参数: 到达容差={self.goal_tolerance}m, 角度死区={self.angle_deadzone_degrees}°, 线速度系数={self.linear_velocity_factor}')
        self.get_logger().info(f'PID控制: 启用={self.enable_pid_linear}, Kp={self.linear_kp}, Ki={self.linear_ki}, Kd={self.linear_kd}')
        self.get_logger().info(f'安全参数: 检测范围={self.obstacle_detection_range}m, 障碍物检测={self.enable_obstacle_detection}, 横向偏差检测={self.enable_lateral_detection}')
        self.get_logger().info('轨迹输入: 通过/path topic从point_publish模块接收')
        self.get_logger().info('==============================================')

    def goal_callback(self, msg: PoseStamped):
        """目标点消息回调函数"""
        # 提取目标点坐标
        goal_x = msg.pose.position.x
        goal_y = msg.pose.position.y
        
        # 更新当前目标点
        self.current_goal = np.array([goal_x, goal_y])
        self.goal_received = True
        
        # 目标信息打印频率控制
        if not hasattr(self, '_goal_counter'):
            self._goal_counter = 0
        self._goal_counter += 1
        
        if self._goal_counter % self.control_print_frequency == 0:
            self.get_logger().info(f'=== NEW GOAL RECEIVED ===')
            self.get_logger().info(f'Goal position: [{goal_x:.3f}, {goal_y:.3f}]')
            self.get_logger().info(f'===========================')

    def path_callback(self, msg: Path):
        """轨迹路径消息回调函数 - 从point_publish模块获取轨迹点"""
        try:
            # 清空现有轨迹点
            self.trajectory_points = []
            
            # 从Path消息中提取轨迹点
            for pose_stamped in msg.poses:
                x = pose_stamped.pose.position.x
                y = pose_stamped.pose.position.y
                z = pose_stamped.pose.position.z
                self.trajectory_points.append([x, y, z])
            
            # 更新轨迹加载状态
            if self.trajectory_points:
                self.trajectory_loaded = True
                self.current_waypoint_index = 0  # 重置当前航点索引
                
                # 路径信息打印频率控制
                if not hasattr(self, '_path_counter'):
                    self._path_counter = 0
                self._path_counter += 1
                
                if self._path_counter % self.control_print_frequency == 0:
                    self.get_logger().info(f'=== NEW PATH RECEIVED ===')
                    self.get_logger().info(f'Path points count: {len(self.trajectory_points)}')
                    self.get_logger().info(f'Start point: [{self.trajectory_points[0][0]:.3f}, {self.trajectory_points[0][1]:.3f}, {self.trajectory_points[0][2]:.3f}]')
                    self.get_logger().info(f'End point: [{self.trajectory_points[-1][0]:.3f}, {self.trajectory_points[-1][1]:.3f}, {self.trajectory_points[-1][2]:.3f}]')
                    self.get_logger().info(f'============================')
            else:
                self.trajectory_loaded = False
                self.get_logger().warn('接收到空的路径消息')
                
        except Exception as e:
            self.get_logger().error(f'处理路径消息时出错: {e}')
            self.trajectory_loaded = False

    def start_navigation_callback(self, msg: Int8):
        """启动导航消息回调函数"""
        if msg.data == 1:  # 值为1表示启动导航
            self.navigation_stopped = False
            self.navigation_paused = False  # 清除暂停状态
            self.get_logger().info('✅ 收到启动导航指令，导航已启动')

    def stop_navigation_callback(self, msg: Int8):
        """停止导航消息回调函数"""
        if msg.data == 1:  # 值为1表示停止导航
            self.navigation_stopped = True
            self.navigation_paused = False  # 清除暂停状态
            self.goal_received = False  # 清除目标状态
            self.current_goal = None
            
            # 立即停止机器人
            self.stop_robot()
            
            self.get_logger().info('🛑 收到停止导航指令，机器人已停止')
    
    def pause_navigation_callback(self, msg: Int8):
        """暂停导航消息回调函数"""
        if msg.data == 1:  # 值为1表示暂停导航
            if not self.navigation_stopped:
                self.navigation_paused = True
                # 立即停止机器人但不清除目标
                self.stop_robot()
                self.get_logger().info('⏸️ 收到暂停导航指令，导航已暂停')
            else:
                self.get_logger().info('导航已停止，忽略暂停指令')
    
    def resume_navigation_callback(self, msg: Int8):
        """恢复导航消息回调函数"""
        if msg.data == 1:  # 值为1表示恢复导航
            if not self.navigation_stopped and self.navigation_paused:
                self.navigation_paused = False
                self.get_logger().info('▶️ 收到恢复导航指令，导航已恢复')
            elif self.navigation_stopped:
                self.get_logger().info('导航已停止，请使用/start_navigation重新启动')
            else:
                self.get_logger().info('导航未暂停，忽略恢复指令')

    def odom_callback(self, msg: Odometry):
        """里程计回调函数"""
        position = msg.pose.pose.position
        orientation = msg.pose.pose.orientation
        
        # 更新机器人位置
        self.robot_pose = np.array([
            position.x,
            position.y,
            self.quaternion_to_yaw(orientation)
        ])
        
        # 定期打印当前位置（每隔几次回调打印一次避免信息过多）
        if not hasattr(self, '_odom_counter'):
            self._odom_counter = 0
        self._odom_counter += 1
        """
        if self._odom_counter % self.odometry_print_frequency == 0:  # 根据参数设置的频率打印
            self.get_logger().info(
                f'Robot pose: x={self.robot_pose[0]:.3f}, y={self.robot_pose[1]:.3f}, '
                f'theta={math.degrees(self.robot_pose[2]):.1f}°'
            )
        """
        # 实时打印后两个轨迹点的yaw角度信息
        if self.trajectory_loaded and self._odom_counter % self.trajectory_yaw_print_frequency == 0:
            trajectory_result = self.calculate_next_two_trajectory_points()
            if trajectory_result['success'] and len(trajectory_result['next_points']) >= 2:
                point1 = trajectory_result['next_points'][0]
                point2 = trajectory_result['next_points'][1]
                """
                self.get_logger().info(
                    f'=== TRAJECTORY YAW INFO ===\n'
                    f'Next Point 1: [{point1["point"][0]:.3f}, {point1["point"][1]:.3f}] '
                    f'yaw={math.degrees(point1["yaw"]):.1f}° (index: {point1["index"]})\n'
                    f'Next Point 2: [{point2["point"][0]:.3f}, {point2["point"][1]:.3f}] '
                    f'yaw={math.degrees(point2["yaw"]):.1f}° (index: {point2["index"]})\n'
                    f'Current nearest index: {trajectory_result["current_index"]}\n'
                    f'==========================='
                )
                """
            elif self.trajectory_loaded:
                self.get_logger().info(f'轨迹计算状态: {trajectory_result["message"]}')
        
        # 执行Pure Pursuit控制
        self.goal_pursuit_control()

    def lateral_deviation_callback(self, msg: LateralDeviation):
        """
        横向偏差消息回调函数
        当接收到横向偏差数据时，设置横向偏差检测标志
        这表示车辆偏离轨迹过远，需要停车
        """
        # 如果横向偏差检测被禁用，跳过处理
        if not self.enable_lateral_detection:
            self.lateral_deviation_detected = False
            return
        
        # 当接收到横向偏差数据时，设置横向偏差检测标志
        self.lateral_deviation_detected = True
        
        self.get_logger().warn(
            f'接收到横向偏差数据: lateral_error={msg.lateral_error_m:.3f}m, '
            f'heading_error={msg.heading_error_rad:.3f}rad - 触发安全停车'
        )

    def lidar_callback(self, msg: PointCloud2):
        """
        激光雷达数据回调函数（优化版本 - 参考C++版本实现）
        ======================================================
        
        处理激光雷达点云数据，进行实时障碍物检测。
        优化包括：
        1. 点云降采样处理 - 提高效率
        2. 期望航向计算 - 从轨迹点计算期望航向用于障碍物过滤
        3. 点云坐标旋转 - 将点云坐标旋转到期望航向坐标系
        4. 基于期望航向的角度过滤 - 只考虑前方可配置角度范围内的点
        5. 参数化的障碍物计数阈值
        
        参数:
            msg: 激光雷达点云消息
        """
        # 如果障碍物检测被禁用，跳过处理
        if not self.enable_obstacle_detection:
            self.obstacle_detected = False
            self.emergency_stop = False
            return
            
        try:
            # 将PointCloud2转换为点列表
            points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True))
            """
            # 点云降采样处理 - 提高效率（简化版本，使用numpy进行降采样）
            if len(points) > 1000:  # 如果点数过多，进行降采样
                step = max(1, len(points) // 1000)  # 计算步长
                points = points[::step]  # 每隔step个点取一个
            
            # 获取期望航向用于障碍物过滤
            expected_yaw = self.robot_pose[2]  # 默认使用当前机器人航向
            trajectory_result = self.calculate_next_two_trajectory_points()
            if trajectory_result['success'] and trajectory_result['next_points']:
                expected_yaw = trajectory_result['next_points'][0]['yaw']
            """
            # 临时障碍物检测状态
            temp_obstacle_detected = False
            temp_emergency_stop = False
            
            # 在安全区域内检查障碍物
            closest_obstacle_distance = float('inf')
            obstacle_count = 0
            
            # 车辆几何参数 (基于实际雷达坐标系: z轴为前进方向, x轴为向下, y轴为左右)
            vehicle_front_limit = 0.7   # z轴正方向的车辆前边界
            vehicle_rear_limit = -2.0   # x轴负方向的车辆高度检查边界
            vehicle_half_width = self.track_width / 2 + 1.0  # 车辆半宽度加安全余量
            
            for point in points:
                x, y, z = point
                #self.get_logger().info(f'原始点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                
                # 过滤车辆本体区域的点云   
                if (abs(y) < 1.0 and 
                    z > 0.5 and 
                    x > 0.7):    
         
                    # 计算点云相对于期望航向的角度                    
                    distance = math.sqrt(y**2 + x**2)
                    self.get_logger().info(f'距离: {distance:.3f}')
                        # 检查点是否在前方检测范围内
                    if distance < self.obstacle_detection_range:
                        obstacle_count += 1
                        closest_obstacle_distance = min(closest_obstacle_distance, distance)
                        self.get_logger().info(f'原始点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                            # 只有当障碍物计数大于阈值时，才进行距离和安全距离的响应
                        if obstacle_count > self.obstacle_count_threshold:
                                # 根据距离设置不同的安全响应级别
                            if distance < self.safety_stop_distance and distance > self.emergency_stop_distance:
                                temp_obstacle_detected = True  # 安全停车
                                self.get_logger().warn(f'障碍物计数: {obstacle_count}')
                                self.get_logger().warn(f'状态: {temp_obstacle_detected}')
                                self.get_logger().warn(f'安全停车！障碍物距离: {distance:.2f}m')
                                self.get_logger().warn(f'安全停车点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                                break
                            if distance < self.emergency_stop_distance:
                               temp_emergency_stop = True  # 紧急停车
                               self.get_logger().warn(f'紧急停车！障碍物距离: {distance:.2f}m')
                               self.get_logger().warn(f'紧急停车点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                               break
            """
                if (abs(y) < vehicle_half_width and 
                    z > vehicle_front_limit and 
                    x > vehicle_rear_limit):
                    #self.get_logger().info(f'原始点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                    # 将点云坐标旋转到期望航向坐标系
                    #print(f'expected_yaw: {expected_yaw}')
                    rotated_x = y * math.cos(expected_yaw) - z * math.sin(expected_yaw)
                    rotated_y = y * math.sin(expected_yaw) + z * math.cos(expected_yaw)
                    
                    # 计算点云相对于期望航向的角度
                    point_yaw = math.atan2(rotated_y, rotated_x)
                    self.get_logger().info(f'旋转后点云数据: rotated_x={rotated_x:.3f}, rotated_y={rotated_y:.3f}, point_yaw={point_yaw:.3f},excpected_yaw={expected_yaw:.3f}')
                    # 基于期望航向过滤障碍物点云（只考虑前方可配置角度范围内的点）
                    yaw_diff = abs(point_yaw)
                    
                    if yaw_diff < self.yaw_filter_angle*math.pi/180:  # 可配置的角度范围
                        # 计算前方障碍物到车辆的距离
                        #distance = math.sqrt(y**2 + z**2)
                        distance = math.sqrt(y**2 + x**2)
                        self.get_logger().info(f'距离: {distance:.3f}')
                        # 检查点是否在前方检测范围内
                        if distance < self.obstacle_detection_range:
                            obstacle_count += 1
                            closest_obstacle_distance = min(closest_obstacle_distance, distance)
                            
                            # 只有当障碍物计数大于阈值时，才进行距离和安全距离的响应
                            if obstacle_count > self.obstacle_count_threshold:
                                # 根据距离设置不同的安全响应级别
                                if distance < self.safety_stop_distance and distance > self.emergency_stop_distance:
                                    temp_obstacle_detected = True  # 安全停车
                                    self.get_logger().warn(f'障碍物计数: {obstacle_count}')
                                    self.get_logger().warn(f'状态: {temp_obstacle_detected}')
                                    self.get_logger().warn(f'安全停车！障碍物距离: {distance:.2f}m')
                                    self.get_logger().warn(f'安全停车点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                                    break
                            if distance < self.emergency_stop_distance:
                                temp_emergency_stop = True  # 紧急停车
                                self.get_logger().warn(f'紧急停车！障碍物距离: {distance:.2f}m')
                                self.get_logger().warn(f'紧急停车点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                                break
            """
            # 更新障碍物状态（避免状态在回调间被重置）
            self.obstacle_detected = temp_obstacle_detected
            self.emergency_stop = temp_emergency_stop
            
            # 如果检测到障碍物，立即发布停车信号
            if temp_obstacle_detected or temp_emergency_stop:
                stop_msg = Int8()
                stop_msg.data = 1  # 1表示停车状态
                self.stop_publisher.publish(stop_msg)
                if temp_obstacle_detected:
                    self.get_logger().warn('🛑 发布停车信号 - 检测到障碍物')
                else:
                    self.get_logger().warn('🚨 发布停车信号 - 紧急停车')
            else:
                # 没有障碍物时发布恢复信号
                stop_msg = Int8()
                stop_msg.data = 0  # 0表示恢复状态
                self.stop_publisher.publish(stop_msg)
            
            # 障碍物检测信息打印频率控制
            if obstacle_count > 0:
                if not hasattr(self, '_obstacle_counter'):
                    self._obstacle_counter = 0
                self._obstacle_counter += 1
            
        except Exception as e:
            self.get_logger().warn(f'处理激光雷达数据时出错: {e}')

    def goal_pursuit_control(self):
        """Pure Pursuit算法控制逻辑（单目标点模式，集成障碍物检测和导航停止）"""
        # === 检查外部停止导航指令（最高优先级） ===
        if self.navigation_stopped:
            self.stop_robot()
            return
        
        # === 检查导航暂停状态 ===
        if self.navigation_paused:
            self.stop_robot()
            return
            
        if not self.goal_received:
            if not hasattr(self, '_no_goal_counter'):
                self._no_goal_counter = 0
            self._no_goal_counter += 1
            if self._no_goal_counter % self.no_goal_warning_frequency == 0:  # 根据参数设置的频率警告
                self.get_logger().warn('No goal received yet, waiting for /local_goal topic')
            return
            
        if self.current_goal is None:
            self.get_logger().warn('No valid goal available')
            return
        
        # === 障碍物安全检查（次高优先级） ===
        if self.enable_obstacle_detection:

            if self.obstacle_detected:
                self.get_logger().warn('⚠️ 检测到障碍物，安全停车')
                self.stop_robot()
                return
            elif self.emergency_stop:
                self.get_logger().warn('🚨 紧急停车！前方有障碍物过近')
                self.stop_robot()
                return 
        
        # === 横向偏差安全检查（次高优先级） ===
        if self.enable_lateral_detection:
            if self.lateral_deviation_detected:
                self.get_logger().warn('⚠️ 检测到横向偏差过大，安全停车')
                self.stop_robot()
                return
        
        # 检查是否到达目标点
        distance_to_goal = np.linalg.norm(self.robot_pose[:2] - self.current_goal)
        
        # 控制状态信息打印频率控制
        if not hasattr(self, '_control_counter'):
            self._control_counter = 0
        self._control_counter += 1
        
        if self._control_counter % self.control_print_frequency == 0:
            """
            self.get_logger().info(
                f'--- GOAL STATUS ---\n'
                f'Current goal: [{self.current_goal[0]:.3f}, {self.current_goal[1]:.3f}]\n'
                f'Distance to goal: {distance_to_goal:.3f}m\n'
                f'Navigation stopped: {self.navigation_stopped}\n'
                f'Obstacle status: Emergency={self.emergency_stop}, Detected={self.obstacle_detected}'
            )
            """
        # 如果到达目标点，停止机器人
        if distance_to_goal < self.goal_tolerance:  # 到达阈值
            self.get_logger().info('🎯 GOAL REACHED! Stopping robot.')
            self.stop_robot()
            self.goal_received = False
            self.current_goal = np.array([0.0, 0.0])
            return
        
        # 发布当前目标点
        self.publish_current_waypoint(self.current_goal)
        
        # 计算Pure Pursuit控制
        control = self.calculate_pure_pursuit(self.current_goal)
        if control is not None:
            self.publish_velocity(control)

    def calculate_pure_pursuit(self, target):
        """计算Pure Pursuit控制输出（优化版本 - 平滑速度过渡）"""
        # 计算到目标点的距离和角度
        dx = target[0] - self.robot_pose[0]
        dy = target[1] - self.robot_pose[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # 目标角度
        target_angle = math.atan2(dy, dx)
        
        # 角度误差
        angle_error = target_angle - self.robot_pose[2]
        # 归一化角度到[-pi, pi]
        angle_error = math.atan2(math.sin(angle_error), math.cos(angle_error))
        
        # 平滑线速度控制 - 根据PID开关选择算法
        if self.enable_pid_linear:
            # 使用PID算法计算线速度
            linear_vel = self.calculate_pid_linear_velocity(distance)
        else:
            # 使用原始Pure Pursuit算法计算线速度 - 适应车辆最小速度0.4m/s的硬件限制
            if distance > self.approach_slow_distance:
                # 远距离：正常速度计算
                base_vel = distance * self.linear_velocity_factor
                linear_vel = min(self.max_linear_velocity, max(base_vel, self.min_linear_velocity))
            elif distance > self.smooth_stop_distance:
                # 接近目标：在最小速度和计算速度之间平滑过渡
                base_vel = distance * self.linear_velocity_factor
                # 使用线性插值在最小速度和较高速度之间平滑过渡
                progress = (distance - self.smooth_stop_distance) / (self.approach_slow_distance - self.smooth_stop_distance)
                target_vel = self.min_linear_velocity + progress * (self.max_linear_velocity * 0.8 - self.min_linear_velocity)
                linear_vel = max(self.min_linear_velocity, min(target_vel, base_vel))
            else:
                # 很近距离：准备停车，但保持最小速度直到到达
                if distance > self.goal_tolerance:
                    linear_vel = self.min_linear_velocity
                else:
                    linear_vel = 0.0  # 到达目标点才完全停止
        
        # 改进的角速度控制 - 使用参数化的分层控制
        if abs(angle_error) < self.angle_deadzone:
            angular_vel = 0.0
            control_type = "deadzone"
        else:
            # 改进的距离调节策略 - 根据参数化的距离分层控制
            if distance > self.far_distance_threshold:
                # 远距离：最高响应性，不限制角速度
                distance_factor = 1.0
                angular_gain = self.far_distance_angular_gain
                control_type = "far_distance"
            elif distance > self.medium_distance_threshold:
                # 中距离：高响应性
                distance_factor = 1.0
                angular_gain = self.medium_distance_angular_gain
                control_type = "medium_distance" 
            elif distance > self.close_distance_threshold:
                # 中近距离：平衡响应性与稳定性
                distance_factor = self.close_distance_factor
                angular_gain = self.close_distance_angular_gain
                control_type = "medium_close"
            else:
                # 近距离：优先稳定性，但保持基本响应性
                # 防止除零错误
                if self.close_distance_threshold > 0:
                    distance_factor = max(self.min_distance_factor, distance / self.close_distance_threshold)
                else:
                    distance_factor = self.min_distance_factor
                angular_gain = self.very_close_angular_gain
                control_type = f"close_distance (factor: {distance_factor:.2f})"
            
            # 角度比例控制
            angular_vel = angle_error * angular_gain * distance_factor
            
            # 限制最大角速度
            angular_vel = max(-self.max_angular_velocity, min(self.max_angular_velocity, angular_vel))
            control_type += f" (gain: {angular_gain}, factor: {distance_factor:.2f})"
        
        # 控制计算详细信息打印频率控制
        if self._control_counter % self.control_print_frequency == 0:
            algorithm_type = "PID" if self.enable_pid_linear else "Pure Pursuit"
            self.get_logger().info(
                f'--- CONTROL CALCULATION ---\n'
                f'Algorithm: {algorithm_type} (Linear) + Pure Pursuit (Angular)\n'
                f'Target angle: {math.degrees(target_angle):.1f}°\n'
                f'Current heading: {math.degrees(self.robot_pose[2]):.1f}°\n'
                f'Angle error: {math.degrees(angle_error):.1f}°\n'
                f'Distance: {distance:.3f}m\n'
                f'Control type: {control_type}\n'
                f'Control output: linear_vel={linear_vel:.3f} m/s, angular_vel={angular_vel:.3f} rad/s\n'
                f'---------------------------'
            )
        
        return [linear_vel, angular_vel]

    def calculate_pid_linear_velocity(self, distance):
        """计算PID线速度输出"""
        current_time = time.time()
        
        if self.pid_previous_time is None:
            self.pid_previous_time = current_time
            dt = 0.01  # 初始时间步长
        else:
            dt = current_time - self.pid_previous_time
            if dt <= 0:
                dt = 0.01  # 防止除零错误
        
        # PID计算: 以距离作为误差
        error = distance
        
        # 比例项
        proportional = self.linear_kp * error
        
        # 积分项
        self.pid_integral += error * dt
        # 积分限幅防止积分饱和
        self.pid_integral = max(-self.pid_integral_limit, 
                               min(self.pid_integral_limit, self.pid_integral))
        integral = self.linear_ki * self.pid_integral
        
        # 微分项
        derivative = self.linear_kd * (error - self.pid_previous_error) / dt
        
        # PID输出
        pid_output = proportional + integral + derivative
        
        # 应用车辆硬件限制
        linear_vel = max(0.0, min(self.max_linear_velocity, pid_output))
        
        # 如果输出大于0但小于最小速度，设为最小速度
        if linear_vel > 0.0 and linear_vel < self.min_linear_velocity:
            linear_vel = self.min_linear_velocity
            
        # 如果距离小于目标容差，停止
        if distance < self.goal_tolerance:
            linear_vel = 0.0
            # 重置PID状态
            self.pid_integral = 0.0
            
        # 更新PID状态
        self.pid_previous_error = error
        self.pid_previous_time = current_time
        
        return linear_vel

    def publish_current_waypoint(self, target):
        """发布当前目标航点"""
        waypoint_msg = PointStamped()
        waypoint_msg.header.stamp = self.get_clock().now().to_msg()
        waypoint_msg.header.frame_id = 'map'
        waypoint_msg.point.x = float(target[0])
        waypoint_msg.point.y = float(target[1])
        waypoint_msg.point.z = 0.0
        self.waypoint_publisher.publish(waypoint_msg)

    def stop_robot(self):
        """停止机器人 - 接收/stop_navigation时输出线速度0.0"""
        twist = Twist()
        twist.linear.x = 0.0  # 接收/stop_navigation时，线速度必须设为0.0
        twist.angular.z = 0.0
        self.cmd_vel_publisher.publish(twist)

    def publish_velocity(self, control):
        """发布速度命令 - 适应车辆硬件限制"""
        twist = Twist()
        linear_vel = float(control[0])
        # 车辆硬件限制：只有速度≥0.4才能行驶，否则停止
        if linear_vel > 0.0 and linear_vel < self.min_linear_velocity:
            linear_vel = self.min_linear_velocity
        twist.linear.x = linear_vel
        twist.angular.z = float(control[1])
        self.cmd_vel_publisher.publish(twist)

    def quaternion_to_yaw(self, q):
        """Convert quaternion to yaw angle."""
        return np.arctan2(2.0 * (q.w * q.z + q.x * q.y), 1.0 - 2.0 * (q.y ** 2 + q.z ** 2))

    def find_nearest_waypoint_index(self, current_pos):
        """根据当前位置找到最近的轨迹点索引"""
        if not self.trajectory_loaded or not self.trajectory_points:
            return None
        
        min_distance = float('inf')
        nearest_index = 0
        
        for i, waypoint in enumerate(self.trajectory_points):
            distance = math.sqrt(
                (current_pos[0] - waypoint[0])**2 + 
                (current_pos[1] - waypoint[1])**2
            )
            if distance < min_distance:
                min_distance = distance
                nearest_index = i
        
        return nearest_index
    
    def calculate_next_two_trajectory_points(self):
        """
        根据当前无人车位置，计算对应的后两个期望轨迹点并算出它们的yaw（航向角）
        
        Returns:
            dict: {
                'success': bool,              # 是否成功计算
                'current_index': int,         # 当前最近轨迹点索引
                'next_points': [              # 后两个期望轨迹点
                    {
                        'point': [x, y, z],  # 坐标
                        'yaw': float,         # 航向角（弧度）
                        'index': int          # 在轨迹中的索引
                    },
                    {...}
                ],
                'message': str                # 状态信息
            }
        """
        result = {
            'success': False,
            'current_index': None,
            'next_points': [],
            'message': ''
        }
        
        # 检查轨迹是否已加载
        if not self.trajectory_loaded:
            result['message'] = '轨迹未加载或轨迹模式未启用'
            return result
        
        if len(self.trajectory_points) < 2:
            result['message'] = '轨迹点数量不足（需要至少2个点）'
            return result
        
        # 查找当前位置最近的轨迹点
        nearest_index = self.find_nearest_waypoint_index(self.robot_pose[:2])
        if nearest_index is None:
            result['message'] = '无法找到最近的轨迹点'
            return result
        
        result['current_index'] = nearest_index
        
        # 计算后两个期望轨迹点
        trajectory_length = len(self.trajectory_points)
        next_points = []
        
        # 第一个期望点：最近点的下一个点
        next_index_1 = nearest_index + 1
        if next_index_1 < trajectory_length:
            point_1 = self.trajectory_points[next_index_1]
            yaw_1 = self.calculate_yaw_to_point(self.trajectory_points[nearest_index], point_1)
            next_points.append({
                'point': point_1,
                'yaw': yaw_1,
                'index': next_index_1
            })
        else:
            # 处理轨迹末尾情况：使用最后一个点，并计算从倒数第二个点到最后一个点的航向角
            if trajectory_length >= 2:
                point_1 = self.trajectory_points[-1]  # 最后一个点
                yaw_1 = self.calculate_yaw_to_point(self.trajectory_points[-2], point_1)
                next_points.append({
                    'point': point_1,
                    'yaw': yaw_1,
                    'index': trajectory_length - 1
                })
                result['message'] += '第一个期望点已达到轨迹末尾; '
        
        # 第二个期望点：第一个点的下一个点
        next_index_2 = nearest_index + 2
        if next_index_2 < trajectory_length:
            point_2 = self.trajectory_points[next_index_2]
            yaw_2 = self.calculate_yaw_to_point(self.trajectory_points[next_index_1], point_2)
            next_points.append({
                'point': point_2,
                'yaw': yaw_2,
                'index': next_index_2
            })
        else:
            # 处理轨迹末尾情况
            if len(next_points) > 0:
                # 如果第一个点存在，第二个点超出范围，则延用第一个点的航向角
                point_2 = list(next_points[0]['point'])  # 复制第一个点的位置
                yaw_2 = next_points[0]['yaw']  # 使用相同的航向角
                next_points.append({
                    'point': point_2,
                    'yaw': yaw_2,
                    'index': next_points[0]['index']
                })
                result['message'] += '第二个期望点已达到轨迹末尾，复用第一个点; '
            else:
                result['message'] += '无法生成第二个期望点; '
        
        result['next_points'] = next_points
        result['success'] = len(next_points) > 0
        
        if result['success'] and not result['message']:
            result['message'] = '成功计算期望轨迹点'
        
        return result
    
    def calculate_yaw_to_point(self, from_point, to_point):
        """计算从一个点到另一个点的航向角（yaw）"""
        dx = to_point[0] - from_point[0]
        dy = to_point[1] - from_point[1]
        yaw = math.atan2(dy, dx)
        return yaw
    
    def get_trajectory_status(self):
        """获取轨迹状态信息"""
        return {
            'trajectory_loaded': self.trajectory_loaded,
            'trajectory_source': '/path topic (from point_publish module)',
            'total_points': len(self.trajectory_points) if self.trajectory_loaded else 0,
            'current_waypoint_index': self.current_waypoint_index,
            'robot_position': self.robot_pose.tolist()
        }


def main(args=None):
    rclpy.init(args=args)
    navigator = DifferentialTrackedNavigator()
    rclpy.spin(navigator)
    navigator.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()