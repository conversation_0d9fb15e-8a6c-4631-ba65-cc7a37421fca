cmake_minimum_required(VERSION 3.16)
project(differential_tracked)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_python REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclpy REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(std_srvs REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(tf2 REQUIRED)
find_package(pcl_conversions REQUIRED)
find_package(PCL REQUIRED)
find_package(global_traj_generate REQUIRED)

# Include PCL directories
include_directories(${PCL_INCLUDE_DIRS})
link_directories(${PCL_LIBRARY_DIRS})
add_definitions(${PCL_DEFINITIONS})

# Include project headers
include_directories(include)

# C++ executable - differential_tracked_navigator_cpp
add_executable(differential_tracked_navigator_cpp
  src/differential_tracked_navigator.cpp
)

ament_target_dependencies(differential_tracked_navigator_cpp
  rclcpp
  geometry_msgs
  nav_msgs
  sensor_msgs
  std_msgs
  std_srvs
  tf2_geometry_msgs
  tf2
  pcl_conversions
  PCL
  global_traj_generate
)

target_link_libraries(differential_tracked_navigator_cpp
  ${PCL_LIBRARIES}
)

install(TARGETS
  differential_tracked_navigator_cpp
  DESTINATION lib/${PROJECT_NAME}
)

# Install Python Pure Pursuit navigator
install(PROGRAMS
  differential_tracked_navigator/differential_tracked_navigator.py
  DESTINATION lib/${PROJECT_NAME}
  RENAME differential_tracked_navigator
)

# Install launch files
install(DIRECTORY
  launch
  DESTINATION share/${PROJECT_NAME}/
)

# Install config files
install(DIRECTORY
  config
  DESTINATION share/${PROJECT_NAME}/
)

# Install maps folder
install(DIRECTORY
  maps
  DESTINATION share/${PROJECT_NAME}/
)

# Install RViz config
install(DIRECTORY
  rviz
  DESTINATION share/${PROJECT_NAME}/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()