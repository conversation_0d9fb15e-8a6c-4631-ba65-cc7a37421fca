<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>differential_tracked</name>
  <version>2.0.0</version>
  <description>ROS2 navigation controller for differential tracked vehicles - refactored version</description>
  <maintainer email="<EMAIL>">User</maintainer>
  <license>MIT</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <buildtool_depend>ament_cmake_python</buildtool_depend>

  <!-- Core ROS2 dependencies -->
  <depend>rclpy</depend>
  <depend>rclcpp</depend>
  <depend>geometry_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>sensor_msgs</depend>
  <depend>std_msgs</depend>
  <depend>std_srvs</depend>
  
  <!-- C++ specific dependencies -->
  <depend>tf2</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>pcl_conversions</depend>
  <depend>libpcl-all-dev</depend>
  
  <!-- Custom message dependencies -->
  <depend>global_traj_generate</depend>
  
  <!-- Navigation and mapping dependencies -->
  <depend>nav2_msgs</depend>
  <depend>nav2_map_server</depend>
  <depend>nav2_lifecycle_manager</depend>
  
  <!-- Additional dependencies -->
  <depend>tf2_ros</depend>
  <depend>robot_state_publisher</depend>
  
  <!-- Visualization -->
  <depend>rviz2</depend>
  
  <!-- Python dependencies -->
  <exec_depend>python3-numpy</exec_depend>
  <exec_depend>python3-yaml</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>