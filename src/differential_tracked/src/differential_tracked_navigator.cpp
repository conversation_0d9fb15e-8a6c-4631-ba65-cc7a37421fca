/**
 * @file differential_tracked_navigator.cpp
 * @brief 差分履带车导航控制器 - C++版本
 * @details Pure Pursuit + PID控制算法，支持障碍物检测和安全系统
 * <AUTHOR> Navigation Team
 * @version 2.0 - C++重构版本，功能与Python版本完全一致
 */

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/point_stamped.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <std_msgs/msg/empty.hpp>
#include <std_msgs/msg/int8.hpp>
#include "global_traj_generate/msg/lateral_deviation.hpp"
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <cmath>
#include <chrono>
#include <array>
#include <vector>
#include <memory>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <string>

class DifferentialTrackedNavigator : public rclcpp::Node
{
public:
    DifferentialTrackedNavigator() : Node("differential_tracked_navigator")
    {
        RCLCPP_INFO(this->get_logger(), "Initializing Differential Tracked Navigator Node");
        
        // 声明ROS2参数
        declare_parameters();
        
        // 从参数服务器加载参数
        load_parameters();
        
        // 初始化状态变量
        initialize_state_variables();
        
        // 创建订阅者
        create_subscribers();
        
        // 创建发布者
        create_publishers();
        
        RCLCPP_INFO(this->get_logger(), "Subscriptions and Publishers initialized");
        print_parameter_summary();
    }

private:
    // === 状态变量 ===
    std::array<double, 3> robot_pose_;  // [x, y, theta]
    std::array<double, 2> current_goal_;  // [x, y]
    bool goal_received_;
    
    // 轨迹点管理
    std::vector<std::array<double, 3>> trajectory_points_;  // 轨迹点列表 [[x, y, z], ...]
    int current_waypoint_index_;                            // 当前目标航点索引
    bool trajectory_loaded_;                                // 轨迹是否已加载
    
    // 障碍物检测状态
    bool obstacle_detected_;
    bool emergency_stop_;
    
    // 横向偏差检测状态
    bool lateral_deviation_detected_;
    
    // 导航控制状态
    bool navigation_stopped_;
    bool navigation_paused_;     // 导航暂停标志
    
    // PID控制状态变量
    double pid_previous_error_;
    double pid_integral_;
    std::chrono::steady_clock::time_point pid_previous_time_;
    bool pid_time_initialized_;
    
    // 计数器
    int odom_counter_;
    int goal_counter_;
    int control_counter_;
    int obstacle_counter_;
    int no_goal_counter_;
    
    // === 参数变量 ===
    // 车辆物理参数
    double track_width_;
    double max_linear_velocity_;
    double min_linear_velocity_;
    double max_angular_velocity_;
    
    // Pure Pursuit控制参数
    double goal_tolerance_;
    double angle_deadzone_degrees_;
    double angle_deadzone_;  // 转换为弧度
    double linear_velocity_factor_;
    double approach_slow_distance_;
    double smooth_stop_distance_;
    
    // Pure Pursuit距离分层控制增益
    double far_distance_threshold_;
    double far_distance_angular_gain_;
    double medium_distance_threshold_;
    double medium_distance_angular_gain_;
    double close_distance_threshold_;
    double close_distance_angular_gain_;
    double very_close_angular_gain_;
    double close_distance_factor_;
    double min_distance_factor_;
    
    // 障碍物检测和安全参数
    double obstacle_detection_range_;
    double safety_stop_distance_;
    double emergency_stop_distance_;
    bool enable_obstacle_detection_;
    bool enable_lateral_detection_;
    int obstacle_count_threshold_;
    double yaw_filter_angle_degrees_;
    double yaw_filter_angle_;  // 转换为弧度
    double voxel_grid_size_;
    
    // PID线速度控制参数
    bool enable_pid_linear_;
    double linear_kp_;
    double linear_ki_;
    double linear_kd_;
    double pid_integral_limit_;
    
    // 调试和监控参数
    int odometry_print_frequency_;
    int no_goal_warning_frequency_;
    int control_print_frequency_;
    int obstacle_print_frequency_;
    int trajectory_yaw_print_frequency_;
    
    // === ROS2接口 ===
    // 订阅者
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odom_subscriber_;
    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr lidar_subscriber_;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr goal_subscriber_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr stop_navigation_subscriber_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr start_navigation_subscriber_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr pause_navigation_subscriber_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr resume_navigation_subscriber_;
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr path_subscriber_;
    rclcpp::Subscription<global_traj_generate::msg::LateralDeviation>::SharedPtr lateral_deviation_subscriber_;
    
    // 发布者
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_vel_publisher_;
    rclcpp::Publisher<geometry_msgs::msg::PointStamped>::SharedPtr waypoint_publisher_;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr stop_publisher_;
    
    void declare_parameters()
    {
        // 车辆物理参数
        this->declare_parameter("vehicle.track_width", 2.0);
        this->declare_parameter("vehicle.max_linear_velocity", 1.0);
        this->declare_parameter("vehicle.min_linear_velocity", 0.1);
        this->declare_parameter("vehicle.max_angular_velocity", 1.4);
        
        // Pure Pursuit控制参数
        this->declare_parameter("control.goal_tolerance", 0.3);
        this->declare_parameter("control.angle_deadzone_degrees", 2.0);
        this->declare_parameter("control.linear_velocity_factor", 0.6);
        this->declare_parameter("control.approach_slow_distance", 1.2);
        this->declare_parameter("control.smooth_stop_distance", 0.5);
        
        // Pure Pursuit距离分层控制增益
        this->declare_parameter("control.far_distance_threshold", 3.0);
        this->declare_parameter("control.far_distance_angular_gain", 2.5);
        this->declare_parameter("control.medium_distance_threshold", 1.5);
        this->declare_parameter("control.medium_distance_angular_gain", 2.0);
        this->declare_parameter("control.close_distance_threshold", 0.8);
        this->declare_parameter("control.close_distance_angular_gain", 1.5);
        this->declare_parameter("control.very_close_angular_gain", 1.0);
        this->declare_parameter("control.close_distance_factor", 0.8);
        this->declare_parameter("control.min_distance_factor", 0.5);
        
        // 障碍物检测和安全参数
        this->declare_parameter("safety.obstacle_detection_range", 2.0);
        this->declare_parameter("safety.safety_stop_distance", 0.8);
        this->declare_parameter("safety.emergency_stop_distance", 0.4);
        this->declare_parameter("safety.enable_obstacle_detection", true);
        this->declare_parameter("safety.enable_lateral_detection", true);
        this->declare_parameter("safety.obstacle_count_threshold", 30);
        this->declare_parameter("safety.yaw_filter_angle_degrees", 30.0);
        this->declare_parameter("safety.voxel_grid_size", 0.1);
        
        // PID线速度控制参数
        this->declare_parameter("control.enable_pid_linear", false);
        this->declare_parameter("control.linear_kp", 0.8);
        this->declare_parameter("control.linear_ki", 0.1);
        this->declare_parameter("control.linear_kd", 0.05);
        this->declare_parameter("control.pid_integral_limit", 2.0);
        
        // 调试和监控参数
        this->declare_parameter("debug.odometry_print_frequency", 20);
        this->declare_parameter("debug.no_goal_warning_frequency", 50);
        this->declare_parameter("debug.control_print_frequency", 10);
        this->declare_parameter("debug.obstacle_print_frequency", 5);
        this->declare_parameter("debug.trajectory_yaw_print_frequency", 20);
    }
    
    void load_parameters()
    {
        // 车辆物理参数
        track_width_ = this->get_parameter("vehicle.track_width").as_double();
        max_linear_velocity_ = this->get_parameter("vehicle.max_linear_velocity").as_double();
        min_linear_velocity_ = this->get_parameter("vehicle.min_linear_velocity").as_double();
        max_angular_velocity_ = this->get_parameter("vehicle.max_angular_velocity").as_double();
        
        // Pure Pursuit控制参数
        goal_tolerance_ = this->get_parameter("control.goal_tolerance").as_double();
        angle_deadzone_degrees_ = this->get_parameter("control.angle_deadzone_degrees").as_double();
        linear_velocity_factor_ = this->get_parameter("control.linear_velocity_factor").as_double();
        approach_slow_distance_ = this->get_parameter("control.approach_slow_distance").as_double();
        smooth_stop_distance_ = this->get_parameter("control.smooth_stop_distance").as_double();
        
        // Pure Pursuit距离分层控制增益
        far_distance_threshold_ = this->get_parameter("control.far_distance_threshold").as_double();
        far_distance_angular_gain_ = this->get_parameter("control.far_distance_angular_gain").as_double();
        medium_distance_threshold_ = this->get_parameter("control.medium_distance_threshold").as_double();
        medium_distance_angular_gain_ = this->get_parameter("control.medium_distance_angular_gain").as_double();
        close_distance_threshold_ = this->get_parameter("control.close_distance_threshold").as_double();
        close_distance_angular_gain_ = this->get_parameter("control.close_distance_angular_gain").as_double();
        very_close_angular_gain_ = this->get_parameter("control.very_close_angular_gain").as_double();
        close_distance_factor_ = this->get_parameter("control.close_distance_factor").as_double();
        min_distance_factor_ = this->get_parameter("control.min_distance_factor").as_double();
        
        // PID线速度控制参数
        enable_pid_linear_ = this->get_parameter("control.enable_pid_linear").as_bool();
        linear_kp_ = this->get_parameter("control.linear_kp").as_double();
        linear_ki_ = this->get_parameter("control.linear_ki").as_double();
        linear_kd_ = this->get_parameter("control.linear_kd").as_double();
        pid_integral_limit_ = this->get_parameter("control.pid_integral_limit").as_double();
        
        // 障碍物检测和安全参数
        obstacle_detection_range_ = this->get_parameter("safety.obstacle_detection_range").as_double();
        safety_stop_distance_ = this->get_parameter("safety.safety_stop_distance").as_double();
        emergency_stop_distance_ = this->get_parameter("safety.emergency_stop_distance").as_double();
        enable_obstacle_detection_ = this->get_parameter("safety.enable_obstacle_detection").as_bool();
        enable_lateral_detection_ = this->get_parameter("safety.enable_lateral_detection").as_bool();
        obstacle_count_threshold_ = this->get_parameter("safety.obstacle_count_threshold").as_int();
        yaw_filter_angle_degrees_ = this->get_parameter("safety.yaw_filter_angle_degrees").as_double();
        voxel_grid_size_ = this->get_parameter("safety.voxel_grid_size").as_double();
        
        // 转换角度参数为弧度
        yaw_filter_angle_ = yaw_filter_angle_degrees_ * M_PI / 180.0;
        
        // 调试和监控参数
        odometry_print_frequency_ = this->get_parameter("debug.odometry_print_frequency").as_int();
        no_goal_warning_frequency_ = this->get_parameter("debug.no_goal_warning_frequency").as_int();
        control_print_frequency_ = this->get_parameter("debug.control_print_frequency").as_int();
        obstacle_print_frequency_ = this->get_parameter("debug.obstacle_print_frequency").as_int();
        trajectory_yaw_print_frequency_ = this->get_parameter("debug.trajectory_yaw_print_frequency").as_int();
        
        // 转换角度参数为弧度
        angle_deadzone_ = angle_deadzone_degrees_ * M_PI / 180.0;
        
        // 轨迹点将通过/path topic接收，无需从文件加载
    }
    
    void initialize_state_variables()
    {
        // Robot state
        robot_pose_ = {0.0, 0.0, 0.0};
        current_goal_ = {0.0, 0.0};
        goal_received_ = false;
        
        // 轨迹点管理
        trajectory_points_.clear();
        current_waypoint_index_ = 0;
        trajectory_loaded_ = false;
        
        // 障碍物检测状态
        obstacle_detected_ = false;
        emergency_stop_ = false;
        
        // 横向偏差检测状态
        lateral_deviation_detected_ = false;
        
        // 导航控制状态
        navigation_stopped_ = false;
        navigation_paused_ = false;
        
        // 命令状态变量已移除，简化为与Python版本一致的状态管理
        
        // PID控制状态变量
        pid_previous_error_ = 0.0;
        pid_integral_ = 0.0;
        pid_time_initialized_ = false;
        
        // 计数器
        odom_counter_ = 0;
        goal_counter_ = 0;
        control_counter_ = 0;
        obstacle_counter_ = 0;
        no_goal_counter_ = 0;
    }
    
    void create_subscribers()
    {
        odom_subscriber_ = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 10, 
            std::bind(&DifferentialTrackedNavigator::odom_callback, this, std::placeholders::_1));
            
        lidar_subscriber_ = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            "/rslidar_points", 10,
            std::bind(&DifferentialTrackedNavigator::lidar_callback, this, std::placeholders::_1));
            
        goal_subscriber_ = this->create_subscription<geometry_msgs::msg::PoseStamped>(
            "/local_goal", 10,
            std::bind(&DifferentialTrackedNavigator::goal_callback, this, std::placeholders::_1));
            
        stop_navigation_subscriber_ = this->create_subscription<std_msgs::msg::Int8>(
            "/stop_navigation", 10,
            std::bind(&DifferentialTrackedNavigator::stop_navigation_callback, this, std::placeholders::_1));
            
        start_navigation_subscriber_ = this->create_subscription<std_msgs::msg::Int8>(
            "/start_navigation", 10,
            std::bind(&DifferentialTrackedNavigator::start_navigation_callback, this, std::placeholders::_1));
            
        pause_navigation_subscriber_ = this->create_subscription<std_msgs::msg::Int8>(
            "/pause_navigation", 10,
            std::bind(&DifferentialTrackedNavigator::pause_navigation_callback, this, std::placeholders::_1));
            
        resume_navigation_subscriber_ = this->create_subscription<std_msgs::msg::Int8>(
            "/resume_navigation", 10,
            std::bind(&DifferentialTrackedNavigator::resume_navigation_callback, this, std::placeholders::_1));
            
            
        path_subscriber_ = this->create_subscription<nav_msgs::msg::Path>(
            "/path", 10,
            std::bind(&DifferentialTrackedNavigator::path_callback, this, std::placeholders::_1));
            
        lateral_deviation_subscriber_ = this->create_subscription<global_traj_generate::msg::LateralDeviation>(
            "/vehicle/lateral_deviation", 10,
            std::bind(&DifferentialTrackedNavigator::lateral_deviation_callback, this, std::placeholders::_1));
    }
    
    void create_publishers()
    {
        cmd_vel_publisher_ = this->create_publisher<geometry_msgs::msg::Twist>("/cmd_vel", 10);
        waypoint_publisher_ = this->create_publisher<geometry_msgs::msg::PointStamped>("/way_point", 10);
        stop_publisher_ = this->create_publisher<std_msgs::msg::Int8>("/stop", 10);
    }
    
    void print_parameter_summary()
    {
        RCLCPP_INFO(this->get_logger(), "=== Differential Tracked Navigator参数配置 ===");
        RCLCPP_INFO(this->get_logger(), "车辆参数: 履带宽度=%.1fm, 最大线速度=%.1fm/s, 最小线速度=%.1fm/s, 最大角速度=%.1frad/s",
                   track_width_, max_linear_velocity_, min_linear_velocity_, max_angular_velocity_);
        RCLCPP_INFO(this->get_logger(), "控制参数: 到达容差=%.1fm, 角度死区=%.1f°, 线速度系数=%.1f",
                   goal_tolerance_, angle_deadzone_degrees_, linear_velocity_factor_);
        RCLCPP_INFO(this->get_logger(), "PID控制: 启用=%s, Kp=%.1f, Ki=%.1f, Kd=%.2f",
                   enable_pid_linear_ ? "true" : "false", linear_kp_, linear_ki_, linear_kd_);
        RCLCPP_INFO(this->get_logger(), "安全参数: 检测范围=%.1fm, 障碍物检测=%s, 横向偏差检测=%s",
                   obstacle_detection_range_, enable_obstacle_detection_ ? "true" : "false",
                   enable_lateral_detection_ ? "true" : "false");
        RCLCPP_INFO(this->get_logger(), "轨迹输入: 通过/path topic从point_publish模块接收");
        RCLCPP_INFO(this->get_logger(), "==============================================");
    }
    
    void goal_callback(const geometry_msgs::msg::PoseStamped::SharedPtr msg)
    {
        // 提取目标点坐标
        double goal_x = msg->pose.position.x;
        double goal_y = msg->pose.position.y;
        
        // 更新当前目标点
        current_goal_[0] = goal_x;
        current_goal_[1] = goal_y;
        goal_received_ = true;
        
        // 目标信息打印频率控制
        goal_counter_++;
        
        if (goal_counter_ % control_print_frequency_ == 0) {
            RCLCPP_INFO(this->get_logger(), "=== NEW GOAL RECEIVED ===");
            RCLCPP_INFO(this->get_logger(), "Goal position: [%.3f, %.3f]", goal_x, goal_y);
            RCLCPP_INFO(this->get_logger(), "===========================");
        }
    }
    
    void path_callback(const nav_msgs::msg::Path::SharedPtr msg)
    {
        try {
            // 清空现有轨迹点
            trajectory_points_.clear();
            
            // 从Path消息中提取轨迹点
            for (const auto& pose_stamped : msg->poses) {
                double x = pose_stamped.pose.position.x;
                double y = pose_stamped.pose.position.y;
                double z = pose_stamped.pose.position.z;
                trajectory_points_.push_back({x, y, z});
            }
            
            // 更新轨迹加载状态
            if (!trajectory_points_.empty()) {
                trajectory_loaded_ = true;
                current_waypoint_index_ = 0;  // 重置当前航点索引
                
                // 路径信息打印频率控制
                static int path_counter = 0;
                path_counter++;
                
                if (path_counter % control_print_frequency_ == 0) {
                    RCLCPP_INFO(this->get_logger(), "=== NEW PATH RECEIVED ===");
                    RCLCPP_INFO(this->get_logger(), "Path points count: %zu", trajectory_points_.size());
                    if (!trajectory_points_.empty()) {  // 防止空容器访问
                        RCLCPP_INFO(this->get_logger(), "Start point: [%.3f, %.3f, %.3f]", 
                                   trajectory_points_[0][0], trajectory_points_[0][1], trajectory_points_[0][2]);
                        RCLCPP_INFO(this->get_logger(), "End point: [%.3f, %.3f, %.3f]", 
                                   trajectory_points_.back()[0], trajectory_points_.back()[1], trajectory_points_.back()[2]);
                    }
                    RCLCPP_INFO(this->get_logger(), "============================");
                }
            } else {
                trajectory_loaded_ = false;
                RCLCPP_WARN(this->get_logger(), "接收到空的路径消息");
            }
            
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "处理路径消息时出错: %s", e.what());
            trajectory_loaded_ = false;
        }
    }
    
    void start_navigation_callback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        if (msg->data == 1) {  // 值为1表示启动导航
            navigation_stopped_ = false;
            navigation_paused_ = false;  // 清除暂停状态
            RCLCPP_INFO(this->get_logger(), "✅ 收到启动导航指令，导航已启动");
        }
    }
    
    
    void stop_navigation_callback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        if (msg->data == 1) {  // 值为1表示停止导航
            navigation_stopped_ = true;
            navigation_paused_ = false;  // 清除暂停状态
            goal_received_ = false;  // 清除目标状态
            current_goal_[0] = 0.0;
            current_goal_[1] = 0.0;
            
            // 立即停止机器人
            stop_robot();
            
            RCLCPP_INFO(this->get_logger(), "🛑 收到停止导航指令，机器人已停止");
        }
    }
    
    void pause_navigation_callback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 暂停导航消息回调函数
        if (msg->data == 1) {  // 值为1表示暂停导航
            if (!navigation_stopped_) {
                navigation_paused_ = true;
                // 立即停止机器人但不清除目标
                stop_robot();
                RCLCPP_INFO(this->get_logger(), "⏸️ 收到暂停导航指令，导航已暂停");
            } else {
                RCLCPP_INFO(this->get_logger(), "导航已停止，忽略暂停指令");
            }
        }
    }
    
    void resume_navigation_callback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 恢复导航消息回调函数
        if (msg->data == 1) {  // 值为1表示恢复导航
            if (!navigation_stopped_ && navigation_paused_) {
                navigation_paused_ = false;
                RCLCPP_INFO(this->get_logger(), "▶️ 收到恢复导航指令，导航已恢复");
            } else if (navigation_stopped_) {
                RCLCPP_INFO(this->get_logger(), "导航已停止，请使用/start_navigation重新启动");
            } else {
                RCLCPP_INFO(this->get_logger(), "导航未暂停，忽略恢复指令");
            }
        }
    }
    
    void odom_callback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        auto position = msg->pose.pose.position;
        auto orientation = msg->pose.pose.orientation;
        
        // 更新机器人位置
        robot_pose_[0] = position.x;
        robot_pose_[1] = position.y;
        robot_pose_[2] = quaternion_to_yaw(orientation);
        
        // 定期打印当前位置（每隔几次回调打印一次避免信息过多）
        odom_counter_++;
        
        if (odom_counter_ % odometry_print_frequency_ == 0) {  // 根据参数设置的频率打印
            RCLCPP_INFO(this->get_logger(),
                       "Robot pose: x=%.3f, y=%.3f, theta=%.1f°",
                       robot_pose_[0], robot_pose_[1], robot_pose_[2] * 180.0 / M_PI);
        }
        
        // 实时打印后两个轨迹点的yaw角度信息
        if (trajectory_loaded_ && odom_counter_ % trajectory_yaw_print_frequency_ == 0) {
            auto trajectory_result = calculate_next_two_trajectory_points();
            if (trajectory_result.success && trajectory_result.next_points.size() >= 2) {
                // 注释掉的代码，用于调试时启用
                /*
                const auto& point1 = trajectory_result.next_points[0];
                const auto& point2 = trajectory_result.next_points[1];
                RCLCPP_INFO(this->get_logger(),
                           "=== TRAJECTORY YAW INFO ===\n"
                           "Next Point 1: [%.3f, %.3f] yaw=%.1f° (index: %d)\n"
                           "Next Point 2: [%.3f, %.3f] yaw=%.1f° (index: %d)\n"
                           "Current nearest index: %d\n"
                           "===========================",
                           point1.point[0], point1.point[1], point1.yaw * 180.0 / M_PI, point1.index,
                           point2.point[0], point2.point[1], point2.yaw * 180.0 / M_PI, point2.index,
                           trajectory_result.current_index);
                           */
            } else if (trajectory_loaded_) {
                RCLCPP_INFO(this->get_logger(), "轨迹计算状态: %s", trajectory_result.message.c_str());
            }
        }
        
        // 执行Pure Pursuit控制
        goal_pursuit_control();
    }
    
    void lateral_deviation_callback(const global_traj_generate::msg::LateralDeviation::SharedPtr msg)
    {
        // 如果横向偏差检测被禁用，跳过处理
        if (!enable_lateral_detection_) {
            lateral_deviation_detected_ = false;
            return;
        }
        
        // 当接收到横向偏差数据时，设置横向偏差检测标志
        // 这表示车辆偏离轨迹过远，需要停车
        lateral_deviation_detected_ = true;
        
        RCLCPP_WARN(this->get_logger(), 
                   "接收到横向偏差数据: lateral_error=%.3fm, heading_error=%.3frad - 触发安全停车",
                   msg->lateral_error_m, msg->heading_error_rad);
    }
    
    void lidar_callback(const sensor_msgs::msg::PointCloud2::SharedPtr msg)
    {
        // 如果障碍物检测被禁用，跳过处理
        if (!enable_obstacle_detection_) {
            obstacle_detected_ = false;
            emergency_stop_ = false;
            return;
        }
        
        try {
            // 将PointCloud2转换为PCL点云
            pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
            pcl::fromROSMsg(*msg, *cloud);
            
            // 点云降采样处理 - 提高效率
            pcl::PointCloud<pcl::PointXYZ>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZ>);
            pcl::VoxelGrid<pcl::PointXYZ> voxel_grid;
            voxel_grid.setInputCloud(cloud);
            voxel_grid.setLeafSize(voxel_grid_size_, voxel_grid_size_, voxel_grid_size_);  // 可配置的体素大小
            voxel_grid.filter(*filtered_cloud);
            
            // 获取期望航向用于障碍物过滤
            double expected_yaw = robot_pose_[2];  // 默认使用当前机器人航向
            auto trajectory_result = calculate_next_two_trajectory_points();
            if (trajectory_result.success && !trajectory_result.next_points.empty()) {
                expected_yaw = trajectory_result.next_points[0].yaw;
            }
	    //RCLCPP_WARN(this->get_logger(), "expected yaw: %.2f", expected_yaw*57.3);

            // double yaw_diff = expected_yaw - robot_pose_[2];
            // yaw_diff = std::atan2(std::sin(yaw_diff), std::cos(yaw_diff));  // 归一化到[-π, π]
            
            // 临时障碍物检测状态
            bool temp_obstacle_detected = false;
            bool temp_emergency_stop = false;
            
            // 在安全区域内检查障碍物
            double closest_obstacle_distance = std::numeric_limits<double>::infinity();
            int obstacle_count = 0;
            
            // 车辆几何参数 (基于实际雷达坐标系: z轴为前进方向, x轴为向下, y轴为左右)
            double vehicle_front_limit = 0.7;   // z轴正方向的车辆前边界
            double vehicle_rear_limit = -2.0;   // x轴负方向的车辆高度检查边界
            double vehicle_half_width = track_width_ / 2 + 1.0;  // 车辆半宽度加安全余量
            
            for (const auto& point : filtered_cloud->points) {
                double x = point.x;
                double y = point.y;
                double z = point.z;
                
                // 过滤车辆本体区域的点云
                if (std::abs(y) < vehicle_half_width && 
                    z > vehicle_front_limit && 
                    x > vehicle_rear_limit) {
                    
                    // 将点云坐标旋转到期望航向坐标系
		            double rotated_yaw = -expected_yaw;
                    double rotated_x = y * std::cos(rotated_yaw) - z * std::sin(rotated_yaw);
                    double rotated_y = y * std::sin(rotated_yaw) + z * std::cos(rotated_yaw);
                    
                    // 计算点云相对于期望航向的角度
                    double point_yaw = std::atan2(rotated_y, rotated_x);

		            // RCLCPP_WARN(this->get_logger(), "(%.3f, %.3f) --> (%.3f, %.3f), point_yaw: %.3f",y,z,rotated_x, rotated_y, point_yaw*57.3);

                    
                    // 基于期望航向过滤障碍物点云（只考虑前方可配置角度范围内的点）
                    double yaw_diff = std::abs(point_yaw);
                    // yaw_diff = std::min(yaw_diff, 2*M_PI - yaw_diff);  // 处理角度环绕
                    // RCLCPP_WARN(this->get_logger(), "yaw_diff: %.3f",yaw_diff* 57.3);
                    if (yaw_diff < yaw_filter_angle_) {  // 可配置的角度范围
                        // 计算前方障碍物到车辆的距离
                        double distance = std::sqrt(y*y + z*z);
		          //	 RCLCPP_WARN(this->get_logger(), "distance: %.3f",distance);
                        
                        // 检查点是否在前方检测范围内
                        if (distance < obstacle_detection_range_) {
                            obstacle_count++;
		     //	    RCLCPP_WARN(this->get_logger(), "障碍物计数: %d", obstacle_count);
                            closest_obstacle_distance = std::min(closest_obstacle_distance, distance);
                            
                            // 只有当障碍物计数大于阈值时，才进行距离和安全距离的响应
                            if (obstacle_count > obstacle_count_threshold_) {
                                // 根据距离设置不同的安全响应级别
                                if (distance < safety_stop_distance_ && distance > emergency_stop_distance_) {
                                    temp_obstacle_detected = true;  // 安全停车
                                    RCLCPP_WARN(this->get_logger(), "障碍物计数: %d", obstacle_count);
                                    RCLCPP_WARN(this->get_logger(), "状态: %s", temp_obstacle_detected ? "true" : "false");
                                    RCLCPP_WARN(this->get_logger(), "安全停车！障碍物距离: %.2fm", distance);
                                    RCLCPP_WARN(this->get_logger(), "安全停车点云数据: x=%.3f, y=%.3f, z=%.3f", x, y, z);
                                    break;
                                } else if (distance < emergency_stop_distance_) {
                                    temp_emergency_stop = true;  // 紧急停车
                                    RCLCPP_WARN(this->get_logger(), "紧急停车！障碍物距离: %.2fm", distance);
                                    RCLCPP_WARN(this->get_logger(), "紧急停车点云数据: x=%.3f, y=%.3f, z=%.3f", x, y, z);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            
            // 更新障碍物状态（避免状态在回调间被重置）
            obstacle_detected_ = temp_obstacle_detected;
            emergency_stop_ = temp_emergency_stop;
            
            // 如果检测到障碍物，立即发布停车信号
            if (temp_obstacle_detected || temp_emergency_stop) {
                auto stop_msg = std_msgs::msg::Int8();
                stop_msg.data = 1;  // 1表示停车状态
                stop_publisher_->publish(stop_msg);
                if (temp_obstacle_detected) {
                    RCLCPP_WARN(this->get_logger(), "🛑 发布停车信号 - 检测到障碍物");
                } else {
                    RCLCPP_WARN(this->get_logger(), "🚨 发布停车信号 - 紧急停车");
                }
            } else {
                // 没有障碍物时发布恢复信号
                auto stop_msg = std_msgs::msg::Int8();
                stop_msg.data = 0;  // 0表示恢复状态
                stop_publisher_->publish(stop_msg);
            }
            
            // 障碍物检测信息打印频率控制
            if (obstacle_count > 0) {
                obstacle_counter_++;
            }
            
        } catch (const std::exception& e) {
            RCLCPP_WARN(this->get_logger(), "处理激光雷达数据时出错: %s", e.what());
        }
    }
    
    void goal_pursuit_control()
    {
        // === 检查外部停止导航指令（最高优先级） ===
        if (navigation_stopped_) {
            stop_robot();
            return;
        }
        
        // === 检查导航暂停状态 ===
        if (navigation_paused_) {
            stop_robot();
            return;
        }
        
        if (!goal_received_) {
            no_goal_counter_++;
            if (no_goal_counter_ % no_goal_warning_frequency_ == 0) {  // 根据参数设置的频率警告
                RCLCPP_WARN(this->get_logger(), "No goal received yet, waiting for /local_goal topic");
            }
            return;
        }
        
        // === 障碍物安全检查（次高优先级） ===
        if (enable_obstacle_detection_) {
            if (obstacle_detected_) {
                RCLCPP_WARN(this->get_logger(), "⚠️ 检测到障碍物，安全停车");
                stop_robot();
                return;
            } else if (emergency_stop_) {
                RCLCPP_WARN(this->get_logger(), "🚨 紧急停车！前方有障碍物过近");
                stop_robot();
                return;
            }
        }
        
        // === 横向偏差安全检查（次高优先级） ===
        if (enable_lateral_detection_) {
            if (lateral_deviation_detected_) {
                RCLCPP_WARN(this->get_logger(), "⚠️ 检测到横向偏差过大，安全停车");
                stop_robot();
                return;
            }
        }
        
        // 检查是否到达目标点
        double distance_to_goal = std::sqrt(
            std::pow(robot_pose_[0] - current_goal_[0], 2) + 
            std::pow(robot_pose_[1] - current_goal_[1], 2)
        );
        
        // 控制状态信息打印频率控制
        control_counter_++;
        
        if (control_counter_ % control_print_frequency_ == 0) {
            RCLCPP_INFO(this->get_logger(),
                       "--- GOAL STATUS ---\n"
                       "Current goal: [%.3f, %.3f]\n"
                       "Distance to goal: %.3fm\n"
                       "Navigation stopped: %s\n"
                       "Obstacle status: Emergency=%s, Detected=%s\n"
                       "Lateral deviation detected: %s",
                       current_goal_[0], current_goal_[1],
                       distance_to_goal,
                       navigation_stopped_ ? "true" : "false",
                       emergency_stop_ ? "true" : "false",
                       obstacle_detected_ ? "true" : "false",
                       lateral_deviation_detected_ ? "true" : "false");
        }
        
        // 如果到达目标点，停止机器人
        if (distance_to_goal < goal_tolerance_) {  // 到达阈值
            RCLCPP_INFO(this->get_logger(), "🎯 GOAL REACHED! Stopping robot.");
            stop_robot();
            goal_received_ = false;
            current_goal_[0] = 0.0;
            current_goal_[1] = 0.0;
            return;
        }
        
        // 发布当前目标点
        publish_current_waypoint(current_goal_);
        
        // 计算Pure Pursuit控制
        auto control = calculate_pure_pursuit(current_goal_);
        if (!control.empty()) {
            publish_velocity(control);
        }
    }
    
    std::vector<double> calculate_pure_pursuit(const std::array<double, 2>& target)
    {
        // 计算到目标点的距离和角度
        double dx = target[0] - robot_pose_[0];
        double dy = target[1] - robot_pose_[1];
        double distance = std::sqrt(dx*dx + dy*dy);
        
        // 目标角度
        double target_angle = std::atan2(dy, dx);
        
        // 角度误差
        double angle_error = target_angle - robot_pose_[2];
        // 归一化角度到[-pi, pi]
        angle_error = std::atan2(std::sin(angle_error), std::cos(angle_error));
        
        // 平滑线速度控制 - 根据PID开关选择算法
        double linear_vel;
        if (enable_pid_linear_) {
            // 使用PID算法计算线速度
            linear_vel = calculate_pid_linear_velocity(distance);
        } else {
            // 使用原始Pure Pursuit算法计算线速度 - 适应车辆最小速度0.4m/s的硬件限制
            if (distance > approach_slow_distance_) {
                // 远距离：正常速度计算
                double base_vel = distance * linear_velocity_factor_;
                linear_vel = std::min(max_linear_velocity_, std::max(base_vel, min_linear_velocity_));
            } else if (distance > smooth_stop_distance_) {
                // 接近目标：在最小速度和计算速度之间平滑过渡
                double base_vel = distance * linear_velocity_factor_;
                // 使用线性插值在最小速度和较高速度之间平滑过渡
                double denominator = approach_slow_distance_ - smooth_stop_distance_;
                if (std::abs(denominator) < 1e-6) {
                    // 防止除零错误
                    linear_vel = min_linear_velocity_;
                } else {
                    double progress = (distance - smooth_stop_distance_) / denominator;
                    double target_vel = min_linear_velocity_ + progress * (max_linear_velocity_ * 0.8 - min_linear_velocity_);
                    linear_vel = std::max(min_linear_velocity_, std::min(target_vel, base_vel));
                }
            } else {
                // 很近距离：准备停车，但保持最小速度直到到达
                if (distance > goal_tolerance_) {
                    linear_vel = min_linear_velocity_;
                } else {
                    linear_vel = 0.0;  // 到达目标点才完全停止
                }
            }
        }
        
        // 改进的角速度控制 - 使用参数化的分层控制
        double angular_vel;
        std::string control_type;
        
        if (std::abs(angle_error) < angle_deadzone_) {
            angular_vel = 0.0;
            control_type = "deadzone";
        } else {
            // 改进的距离调节策略 - 根据参数化的距离分层控制
            double distance_factor;
            double angular_gain;
            
            if (distance > far_distance_threshold_) {
                // 远距离：最高响应性，不限制角速度
                distance_factor = 1.0;
                angular_gain = far_distance_angular_gain_;
                control_type = "far_distance";
            } else if (distance > medium_distance_threshold_) {
                // 中距离：高响应性
                distance_factor = 1.0;
                angular_gain = medium_distance_angular_gain_;
                control_type = "medium_distance";
            } else if (distance > close_distance_threshold_) {
                // 中近距离：平衡响应性与稳定性
                distance_factor = close_distance_factor_;
                angular_gain = close_distance_angular_gain_;
                control_type = "medium_close";
            } else {
                // 近距离：优先稳定性，但保持基本响应性
                // 防止除零错误
                if (close_distance_threshold_ > 0) {
                    distance_factor = std::max(min_distance_factor_, distance / close_distance_threshold_);
                } else {
                    distance_factor = min_distance_factor_;
                }
                angular_gain = very_close_angular_gain_;
                control_type = "close_distance (factor: " + std::to_string(distance_factor) + ")";
            }
            
            // 角度比例控制
            angular_vel = angle_error * angular_gain * distance_factor;
            
            // 限制最大角速度
            angular_vel = std::max(-max_angular_velocity_, std::min(max_angular_velocity_, angular_vel));
            control_type += " (gain: " + std::to_string(angular_gain) + ", factor: " + std::to_string(distance_factor) + ")";
        }
        
        // 控制计算详细信息打印频率控制
        if (control_counter_ % control_print_frequency_ == 0) {
            std::string algorithm_type = enable_pid_linear_ ? "PID" : "Pure Pursuit";
            RCLCPP_INFO(this->get_logger(),
                       "--- CONTROL CALCULATION ---\n"
                       "Algorithm: %s (Linear) + Pure Pursuit (Angular)\n"
                       "Target angle: %.1f°\n"
                       "Current heading: %.1f°\n"
                       "Angle error: %.1f°\n"
                       "Distance: %.3fm\n"
                       "Control type: %s\n"
                       "Control output: linear_vel=%.3f m/s, angular_vel=%.3f rad/s\n"
                       "---------------------------",
                       algorithm_type.c_str(),
                       target_angle * 180.0 / M_PI,
                       robot_pose_[2] * 180.0 / M_PI,
                       angle_error * 180.0 / M_PI,
                       distance,
                       control_type.c_str(),
                       linear_vel, angular_vel);
        }
        
        return {linear_vel, angular_vel};
    }
    
    double calculate_pid_linear_velocity(double distance)
    {
        auto current_time = std::chrono::steady_clock::now();
        
        double dt;
        if (!pid_time_initialized_) {
            pid_previous_time_ = current_time;
            pid_time_initialized_ = true;
            dt = 0.01;  // 初始时间步长
        } else {
            auto dt_duration = current_time - pid_previous_time_;
            dt = std::chrono::duration<double>(dt_duration).count();
            if (dt <= 0) {
                dt = 0.01;  // 防止除零错误
            }
        }
        
        // PID计算: 以距离作为误差
        double error = distance;
        
        // 比例项
        double proportional = linear_kp_ * error;
        
        // 积分项
        pid_integral_ += error * dt;
        // 积分限幅防止积分饱和
        pid_integral_ = std::max(-pid_integral_limit_, 
                                std::min(pid_integral_limit_, pid_integral_));
        double integral = linear_ki_ * pid_integral_;
        
        // 微分项
        double derivative = linear_kd_ * (error - pid_previous_error_) / dt;
        
        // PID输出
        double pid_output = proportional + integral + derivative;
        
        // 应用车辆硬件限制
        double linear_vel = std::max(0.0, std::min(max_linear_velocity_, pid_output));
        
        // 如果输出大于0但小于最小速度，设为最小速度
        if (linear_vel > 0.0 && linear_vel < min_linear_velocity_) {
            linear_vel = min_linear_velocity_;
        }
        
        // 如果距离小于目标容差，停止
        if (distance < goal_tolerance_) {
            linear_vel = 0.0;
            // 重置PID状态
            pid_integral_ = 0.0;
        }
        
        // 更新PID状态
        pid_previous_error_ = error;
        pid_previous_time_ = current_time;
        
        return linear_vel;
    }
    
    void publish_current_waypoint(const std::array<double, 2>& target)
    {
        auto waypoint_msg = geometry_msgs::msg::PointStamped();
        waypoint_msg.header.stamp = this->get_clock()->now();
        waypoint_msg.header.frame_id = "map";
        waypoint_msg.point.x = target[0];
        waypoint_msg.point.y = target[1];
        waypoint_msg.point.z = 0.0;
        waypoint_publisher_->publish(waypoint_msg);
    }
    
    void stop_robot()
    {
        auto twist = geometry_msgs::msg::Twist();
        twist.linear.x = 0.0;  // 接收/stop_navigation时，线速度必须设为0.0
        twist.angular.z = 0.0;
        cmd_vel_publisher_->publish(twist);
    }
    
    void publish_velocity(const std::vector<double>& control)
    {
        auto twist = geometry_msgs::msg::Twist();
        double linear_vel = control[0];
        // 车辆硬件限制：只有速度≥0.4才能行驶，否则停止
        if (linear_vel > 0.0 && linear_vel < min_linear_velocity_) {
            linear_vel = min_linear_velocity_;
        }
        twist.linear.x = linear_vel;
        twist.angular.z = control[1];
        cmd_vel_publisher_->publish(twist);
    }
    
    double quaternion_to_yaw(const geometry_msgs::msg::Quaternion& q)
    {
        return std::atan2(2.0 * (q.w * q.z + q.x * q.y), 1.0 - 2.0 * (q.y * q.y + q.z * q.z));
    }
    
    int find_nearest_waypoint_index(const std::array<double, 2>& current_pos)
    {
        if (!trajectory_loaded_ || trajectory_points_.empty()) {
            return -1;
        }
        
        double min_distance = std::numeric_limits<double>::infinity();
        int nearest_index = 0;
        
        for (size_t i = 0; i < trajectory_points_.size(); ++i) {
            double distance = std::sqrt(
                std::pow(current_pos[0] - trajectory_points_[i][0], 2) + 
                std::pow(current_pos[1] - trajectory_points_[i][1], 2)
            );
            if (distance < min_distance) {
                min_distance = distance;
                nearest_index = static_cast<int>(i);
            }
        }
        
        return nearest_index;
    }
    
    struct TrajectoryPoint {
        std::array<double, 3> point;
        double yaw;
        int index;
    };
    
    struct TrajectoryResult {
        bool success;
        int current_index;
        std::vector<TrajectoryPoint> next_points;
        std::string message;
    };
    
    TrajectoryResult calculate_next_two_trajectory_points()
    {
        TrajectoryResult result;
        result.success = false;
        result.current_index = -1;
        result.message = "";
        
        // 检查轨迹是否已加载
        if (!trajectory_loaded_) {
            result.message = "轨迹未加载或轨迹模式未启用";
            return result;
        }
        
        if (trajectory_points_.size() < 2) {
            result.message = "轨迹点数量不足（需要至少2个点）";
            return result;
        }
        
        // 查找当前位置最近的轨迹点
        std::array<double, 2> robot_pos = {robot_pose_[0], robot_pose_[1]};
        int nearest_index = find_nearest_waypoint_index(robot_pos);
        if (nearest_index < 0) {
            result.message = "无法找到最近的轨迹点";
            return result;
        }
        
        result.current_index = nearest_index;
        
        // 计算后两个期望轨迹点
        int trajectory_length = static_cast<int>(trajectory_points_.size());
        
        // 第一个期望点：最近点的下一个点
        int next_index_1 = nearest_index + 1;
        if (next_index_1 < trajectory_length) {
            TrajectoryPoint point_1;
            point_1.point = trajectory_points_[next_index_1];
            point_1.yaw = calculate_yaw_to_point(trajectory_points_[nearest_index], point_1.point);
            point_1.index = next_index_1;
            result.next_points.push_back(point_1);
        } else {
            // 处理轨迹末尾情况：使用最后一个点，并计算从倒数第二个点到最后一个点的航向角
            if (trajectory_length >= 2) {
                TrajectoryPoint point_1;
                point_1.point = trajectory_points_[trajectory_length - 1];  // 最后一个点
                point_1.yaw = calculate_yaw_to_point(trajectory_points_[trajectory_length - 2], point_1.point);
                point_1.index = trajectory_length - 1;
                result.next_points.push_back(point_1);
                result.message += "第一个期望点已达到轨迹末尾; ";
            }
        }
        
        // 第二个期望点：第一个点的下一个点
        int next_index_2 = nearest_index + 2;
        if (next_index_2 < trajectory_length) {
            TrajectoryPoint point_2;
            point_2.point = trajectory_points_[next_index_2];
            // 确保next_index_1有效
            if (next_index_1 < trajectory_length) {
                point_2.yaw = calculate_yaw_to_point(trajectory_points_[next_index_1], point_2.point);
            } else {
                point_2.yaw = calculate_yaw_to_point(trajectory_points_[trajectory_length - 2], point_2.point);
            }
            point_2.index = next_index_2;
            result.next_points.push_back(point_2);
        } else {
            // 处理轨迹末尾情况
            if (!result.next_points.empty()) {
                // 如果第一个点存在，第二个点超出范围，则延用第一个点的航向角
                TrajectoryPoint point_2;
                point_2.point = result.next_points[0].point;  // 复制第一个点的位置
                point_2.yaw = result.next_points[0].yaw;      // 使用相同的航向角
                point_2.index = result.next_points[0].index;
                result.next_points.push_back(point_2);
                result.message += "第二个期望点已达到轨迹末尾，复用第一个点; ";
            } else {
                result.message += "无法生成第二个期望点; ";
            }
        }
        
        result.success = !result.next_points.empty();
        
        if (result.success && result.message.empty()) {
            result.message = "成功计算期望轨迹点";
        }
        
        return result;
    }
    
    double calculate_yaw_to_point(const std::array<double, 3>& from_point, const std::array<double, 3>& to_point)
    {
        double dx = to_point[0] - from_point[0];
        double dy = to_point[1] - from_point[1];
        return std::atan2(dy, dx);
    }
    
    struct TrajectoryStatus {
        bool trajectory_loaded;
        std::string trajectory_source;
        int total_points;
        int current_waypoint_index;
        std::array<double, 3> robot_position;
    };
    
    TrajectoryStatus get_trajectory_status()
    {
        TrajectoryStatus status;
        status.trajectory_loaded = trajectory_loaded_;
        status.trajectory_source = "/path topic (from point_publish module)";
        status.total_points = trajectory_loaded_ ? static_cast<int>(trajectory_points_.size()) : 0;
        status.current_waypoint_index = current_waypoint_index_;
        status.robot_position = robot_pose_;
        return status;
    }
};

int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto navigator = std::make_shared<DifferentialTrackedNavigator>();
    rclcpp::spin(navigator);
    rclcpp::shutdown();
    return 0;
}
