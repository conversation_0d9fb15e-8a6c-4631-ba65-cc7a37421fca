differential_tracked_navigator:
  ros__parameters:
    # === Pure Pursuit算法专用参数 ===
    
    # 车辆物理参数 (实际使用的参数)
    vehicle.track_width: 1.69                  # 车辆履带间距，用于障碍物检测范围 (米)
    vehicle.max_linear_velocity: 0.8          # Pure Pursuit最大线速度 (m/s)
    vehicle.min_linear_velocity: 0.5          # Pure Pursuit最小线速度 (m/s) - 车辆硬件限制
    vehicle.max_angular_velocity: 1.5         # Pure Pursuit最大角速度 (rad/s)

    # Pure Pursuit控制参数
    control.goal_tolerance: 0.3               # 到达航点的距离阈值 (米)
    control.angle_deadzone_degrees: 2.5       # 角度控制死区 (度)
    control.linear_velocity_factor: 0.6       # 线速度计算系数 (distance * factor) - 提高响应性
    control.approach_slow_distance: 1.2       # 接近减速距离阈值 (米) - 新增参数
    control.smooth_stop_distance: 0.5         # 平滑停止距离阈值 (米) - 新增参数
    
    # PID线速度控制参数
    control.enable_pid_linear: false          # 启用PID线速度控制 (false=Pure Pursuit, true=PID)
    control.linear_kp: 0.8                    # PID线速度比例增益
    control.linear_ki: 0.1                    # PID线速度积分增益  
    control.linear_kd: 0.05                   # PID线速度微分增益
    control.pid_integral_limit: 2.0           # PID积分限幅 (防止积分饱和)
    
    # Pure Pursuit距离分层控制增益
    control.far_distance_threshold: 3.0       # 远距离阈值 (米)
    control.far_distance_angular_gain: 2.0    # 远距离角速度增益
    control.medium_distance_threshold: 1.5    # 中距离阈值 (米)  
    control.medium_distance_angular_gain: 1.6 # 中距离角速度增益
    control.close_distance_threshold: 0.8     # 近距离阈值 (米)
    control.close_distance_angular_gain: 1.3  # 中近距离角速度增益
    control.very_close_angular_gain: 1.0      # 很近距离角速度增益
    control.close_distance_factor: 0.8        # 中近距离距离系数
    control.min_distance_factor: 0.5          # 最小距离响应系数

    # 障碍物检测和安全参数 (实际使用的参数)
    safety.obstacle_detection_range: 5.5      # 障碍物检测范围 (米)
    safety.safety_stop_distance: 4.0          # 安全停车距离 (米)
    safety.emergency_stop_distance: 2.5       # 紧急停车距离 (米) 
    safety.enable_obstacle_detection: true     # 启用障碍物检测
    safety.enable_lateral_detection: false      # 启用横向偏差检测
    safety.obstacle_count_threshold: 20        # 障碍物检测点数阈值 (个) - 超过此数量才触发安全响应
    safety.yaw_filter_angle_degrees: 30.0     # 障碍物检测角度过滤范围 (度) - 只检测前方此角度范围内的障碍物
    safety.voxel_grid_size: 0.2               # 点云降采样体素网格大小 (米) - 用于提高检测效率 

    # 调试和监控参数
    debug.odometry_print_frequency: 200        # 每N次里程计回调打印一次位置信息
    debug.no_goal_warning_frequency: 50       # 每N次控制循环打印无目标警告
    debug.control_print_frequency: 100         # 每N次控制循环打印控制计算信息
    debug.obstacle_print_frequency: 20         # 每N次障碍物检测打印障碍物信息